/**
 * Clean Enterprise Settings CSS
 * Redco Optimizer Plugin
 * Built from scratch for enterprise-grade interface
 */

/* CSS Variables */
:root {
    --redco-primary: #4CAF50;
    --redco-border: #e2e8f0;
    --redco-text: #374151;
    --redco-text-light: #6b7280;
    --redco-bg: #ffffff;
    --redco-bg-light: #f9fafb;
    --redco-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --redco-radius: 8px;
    --redco-transition: all 0.2s ease;
}

/* Settings Page Container */
.redco-optimizer-settings {
    background: var(--redco-bg-light);
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* Settings Header */
.redco-settings-header {
    background: var(--redco-bg);
    border-bottom: 1px solid var(--redco-border);
    padding: 24px 32px;
    margin-bottom: 32px;
}

.settings-header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.settings-title-section h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-title-section p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
}

/* Navigation Tabs */
.redco-nav-tab-wrapper {
    display: flex;
    gap: 0;
    margin: 0 0 32px 0;
    border-bottom: 1px solid var(--redco-border);
    background: var(--redco-bg);
    padding: 0 32px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.redco-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    text-decoration: none;
    color: var(--redco-text-light);
    transition: var(--redco-transition);
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
}

.redco-nav-tab:hover {
    color: var(--redco-primary);
    background: rgba(76, 175, 80, 0.05);
}

.redco-nav-tab.redco-nav-tab-active {
    color: var(--redco-primary);
    border-bottom-color: var(--redco-primary);
    background: transparent;
}

.redco-nav-tab .dashicons {
    font-size: 16px;
    flex-shrink: 0;
}

.redco-nav-tab .tab-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-nav-tab .tab-title {
    font-weight: 600;
    font-size: 14px;
}

.redco-nav-tab .tab-description {
    font-size: 12px;
    opacity: 0.7;
    display: none; /* Hide for clean look */
}

/* Settings Content */
.redco-settings-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 32px 32px 32px;
}

/* Settings Section */
.redco-settings-section {
    margin-bottom: 48px;
}

.settings-section-header {
    margin-bottom: 24px;
}

.settings-section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
}

.settings-section-header p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
}

/* Single Settings Card */
.settings-card {
    background: var(--redco-bg);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    margin-bottom: 24px;
}

.settings-card-content {
    padding: 32px;
}

/* Setting Items */
.setting-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 32px;
    padding: 20px 0;
    border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item:first-child {
    padding-top: 0;
}

.setting-info {
    flex: 1;
    min-width: 0;
}

.setting-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.setting-info p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.setting-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
}

/* Toggle Switch */
.redco-toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    cursor: pointer;
}

.redco-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    border-radius: 24px;
    transition: var(--redco-transition);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: var(--redco-transition);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.redco-toggle-switch input:checked + .toggle-slider {
    background-color: var(--redco-primary);
}

.redco-toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

.redco-toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Form Controls */
.redco-select,
.redco-text-input,
.redco-number-input {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: 6px;
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    min-width: 200px;
}

.redco-select:focus,
.redco-text-input:focus,
.redco-number-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Button */
.button.button-primary {
    background: var(--redco-primary);
    border-color: var(--redco-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: var(--redco-transition);
}

.button.button-primary:hover {
    background: #45a049;
    border-color: #45a049;
}

/* Auto-save feedback */
.setting-item.saving {
    opacity: 0.7;
}

.setting-item.saved {
    border-left: 3px solid var(--redco-primary);
    background: rgba(76, 175, 80, 0.05);
}

.setting-item.error {
    border-left: 3px solid #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

/* Responsive */
@media (max-width: 768px) {
    .redco-settings-header,
    .redco-nav-tab-wrapper,
    .redco-settings-content {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .settings-card-content {
        padding: 24px;
    }
    
    .setting-item {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .setting-control {
        align-self: flex-start;
    }
    
    .redco-nav-tab-wrapper {
        flex-wrap: wrap;
    }
}

/* Hide WordPress default form table */
.redco-settings-form table.form-table {
    display: none;
}

<?php
/**
 * PageSpeed 95+ Optimizer for Redco Optimizer
 *
 * Automatically configures all modules for optimal PageSpeed performance
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_PageSpeed_Optimizer {

    /**
     * Initialize PageSpeed optimizations
     */
    public function init() {
        add_action('admin_init', array($this, 'apply_pagespeed_optimizations'));
        add_action('wp_ajax_redco_apply_pagespeed_config', array($this, 'ajax_apply_pagespeed_config'));
    }

    /**
     * Apply optimal PageSpeed configuration
     */
    public function apply_pagespeed_optimizations() {
        // Only run once or when explicitly requested
        if (get_option('redco_pagespeed_optimized') && !isset($_GET['force_optimize'])) {
            return;
        }

        $this->enable_core_modules();
        $this->configure_optimal_settings();
        
        // Mark as optimized
        update_option('redco_pagespeed_optimized', time());
    }

    /**
     * Enable core performance modules
     */
    private function enable_core_modules() {
        $options = get_option('redco_optimizer_options', array());
        
        // Enable core modules for PageSpeed 95+
        $core_modules = array(
            'page-cache',
            'lazy-load', 
            'css-js-minifier',
            'emoji-stripper',
            'heartbeat-control',
            'query-string-remover',
            'asset-version-remover',
            'critical-resource-optimizer'
        );

        if (!isset($options['modules_enabled'])) {
            $options['modules_enabled'] = array();
        }

        foreach ($core_modules as $module) {
            if (!in_array($module, $options['modules_enabled'])) {
                $options['modules_enabled'][] = $module;
            }
        }

        update_option('redco_optimizer_options', $options);
    }

    /**
     * Configure optimal settings for each module
     */
    private function configure_optimal_settings() {
        // Use centralized configuration for optimal settings
        $modules = array('page-cache', 'lazy-load', 'css-js-minifier', 'heartbeat-control');

        foreach ($modules as $module) {
            $defaults = Redco_Config::get_module_defaults($module);
            if (!empty($defaults)) {
                update_option("redco_{$module}_options", $defaults);
            }
        }

        // Additional modules can be configured here if needed

        // Critical Resource Optimizer - Performance measurement disabled
        update_option('redco_critical-resource-optimizer_options', array(
            'critical_css' => true,
            'defer_non_critical' => true,
            'optimize_js' => true,
            'optimize_fonts' => true,
            'resource_hints' => true,
            'preconnect_google_fonts' => true,
            'preconnect_analytics' => true,
            'preconnect_custom' => '',
            'measure_performance' => false // Disabled for optimal performance
        ));

        // Emoji Stripper - Remove from frontend
        update_option('redco_emoji-stripper_options', array(
            'remove_frontend' => true,
            'remove_admin' => false,
            'remove_feeds' => true,
            'remove_emails' => true
        ));

        // Query String Remover - Enable with minimal exclusions
        update_option('redco_query-string-remover_options', array(
            'remove_css_version' => true,
            'remove_js_version' => true,
            'exclude_handles' => array('jquery-core', 'jquery-migrate')
        ));

        // Asset Version Remover - Enable for better caching
        update_option('redco_asset-version-remover_options', array(
            'remove_css_versions' => true,
            'remove_js_versions' => true,
            'exclude_handles' => array('jquery-core')
        ));
    }

    /**
     * AJAX handler for applying PageSpeed configuration
     */
    public function ajax_apply_pagespeed_config() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $this->enable_core_modules();
        $this->configure_optimal_settings();

        // Clear all caches
        wp_cache_flush();
        
        // Clear plugin caches
        $this->clear_plugin_caches();

        wp_send_json_success(array(
            'message' => 'PageSpeed 95+ configuration applied successfully!',
            'modules_enabled' => 8,
            'expected_improvement' => '20-30 points',
            'next_steps' => array(
                'Test with PageSpeed Insights',
                'Monitor performance metrics',
                'Fine-tune settings if needed'
            )
        ));
    }

    /**
     * Clear all plugin caches
     */
    private function clear_plugin_caches() {
        // Clear page cache
        if (class_exists('Redco_Page_Cache')) {
            $page_cache = new Redco_Page_Cache();
            if (method_exists($page_cache, 'clear_all_cache')) {
                $page_cache->clear_all_cache();
            }
        }

        // Clear minified cache
        if (class_exists('Redco_CSS_JS_Minifier')) {
            $minifier = new Redco_CSS_JS_Minifier();
            if (method_exists($minifier, 'clear_cache')) {
                $minifier->clear_cache();
            }
        }

        // Clear critical CSS cache
        $cache_dir = redco_get_cache_dir() . 'critical-css/';
        if (is_dir($cache_dir)) {
            redco_clear_directory_recursive($cache_dir, false);
        }

        // Clear transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_redco_%'");
    }

    /**
     * Get current optimization status
     */
    public function get_optimization_status() {
        $options = get_option('redco_optimizer_options', array());
        $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
        
        $core_modules = array(
            'page-cache',
            'lazy-load',
            'css-js-minifier', 
            'emoji-stripper',
            'heartbeat-control',
            'query-string-remover',
            'asset-version-remover',
            'critical-resource-optimizer'
        );

        $enabled_count = 0;
        foreach ($core_modules as $module) {
            if (in_array($module, $enabled_modules)) {
                $enabled_count++;
            }
        }

        $optimization_percentage = round(($enabled_count / count($core_modules)) * 100);
        
        return array(
            'modules_enabled' => $enabled_count,
            'total_modules' => count($core_modules),
            'optimization_percentage' => $optimization_percentage,
            'is_optimized' => $optimization_percentage >= 80,
            'last_optimized' => get_option('redco_pagespeed_optimized', 0),
            'expected_pagespeed_score' => $this->calculate_expected_score($optimization_percentage)
        );
    }

    /**
     * Calculate expected PageSpeed score based on optimization level
     */
    private function calculate_expected_score($optimization_percentage) {
        if ($optimization_percentage >= 100) {
            return '85-95+';
        } elseif ($optimization_percentage >= 80) {
            return '75-85';
        } elseif ($optimization_percentage >= 60) {
            return '65-75';
        } elseif ($optimization_percentage >= 40) {
            return '55-65';
        } else {
            return '45-55';
        }
    }

    /**
     * Get performance recommendations
     */
    public function get_performance_recommendations() {
        $status = $this->get_optimization_status();
        $recommendations = array();

        if ($status['optimization_percentage'] < 100) {
            $recommendations[] = array(
                'type' => 'enable_modules',
                'priority' => 'high',
                'message' => 'Enable all core performance modules for maximum PageSpeed improvement',
                'action' => 'Apply PageSpeed 95+ Configuration'
            );
        }

        if (!get_option('redco_pagespeed_optimized')) {
            $recommendations[] = array(
                'type' => 'first_time_setup',
                'priority' => 'critical',
                'message' => 'Run the PageSpeed 95+ optimization to configure all modules optimally',
                'action' => 'Run Optimization Now'
            );
        }

        return $recommendations;
    }
}

// Initialize PageSpeed optimizer
$redco_pagespeed_optimizer = new Redco_PageSpeed_Optimizer();
$redco_pagespeed_optimizer->init();

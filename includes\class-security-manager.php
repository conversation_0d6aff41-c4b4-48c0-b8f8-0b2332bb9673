<?php
/**
 * Security Management System for Redco Optimizer
 * 
 * Enhanced security features and production hardening
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Security_Manager {
    
    /**
     * Security levels
     */
    const LEVEL_LOW = 'low';
    const LEVEL_MEDIUM = 'medium';
    const LEVEL_HIGH = 'high';
    const LEVEL_STRICT = 'strict';
    
    /**
     * Failed login attempts tracking
     */
    private static $failed_attempts = array();
    
    /**
     * Security configuration
     */
    private static $config = array(
        'security_level' => self::LEVEL_MEDIUM,
        'max_failed_attempts' => 5,
        'lockout_duration' => 1800, // 30 minutes
        'enable_file_monitoring' => true,
        'enable_request_filtering' => true,
        'enable_admin_protection' => true
    );
    
    /**
     * Initialize security manager
     */
    public static function init() {
        // Safety check - only initialize if WordPress is fully loaded
        if (!function_exists('wp_verify_nonce') || !function_exists('current_user_can')) {
            return;
        }

        // Load configuration
        self::load_config();
        
        // Add security hooks
        add_action('wp_login_failed', array(__CLASS__, 'handle_failed_login'));
        add_action('wp_login', array(__CLASS__, 'handle_successful_login'), 10, 2);
        add_filter('authenticate', array(__CLASS__, 'check_login_attempts'), 30, 3);
        
        // Add admin security (temporarily disabled for testing)
        if (self::$config['enable_admin_protection'] && Redco_Config::is_development_environment()) {
            add_action('admin_init', array(__CLASS__, 'admin_security_check'));
            // Temporarily disable wp_die handler override
            // add_filter('wp_die_handler', array(__CLASS__, 'secure_wp_die_handler'));
        }

        // Add request filtering (temporarily disabled for testing)
        if (self::$config['enable_request_filtering'] && Redco_Config::is_development_environment()) {
            add_action('init', array(__CLASS__, 'filter_malicious_requests'), 1);
        }
        
        // Add file monitoring
        if (self::$config['enable_file_monitoring']) {
            add_action('redco_security_scan', array(__CLASS__, 'scan_core_files'));
            
            // Schedule security scan
            if (!wp_next_scheduled('redco_security_scan')) {
                wp_schedule_event(time(), 'daily', 'redco_security_scan');
            }
        }
        
        // Add security headers
        add_action('send_headers', array(__CLASS__, 'add_security_headers'));
        
        // Add nonce validation for AJAX (temporarily disabled for testing)
        // add_action('wp_ajax_redco_*', array(__CLASS__, 'validate_ajax_nonce'), 1);
        // add_action('wp_ajax_nopriv_redco_*', array(__CLASS__, 'validate_ajax_nonce'), 1);
    }
    
    /**
     * Handle failed login attempt
     * 
     * @param string $username Username that failed
     */
    public static function handle_failed_login($username) {
        $ip = self::get_client_ip();
        $key = 'failed_login_' . md5($ip . $username);
        
        $attempts = get_transient($key) ?: 0;
        $attempts++;
        
        set_transient($key, $attempts, self::$config['lockout_duration']);
        
        if ($attempts >= self::$config['max_failed_attempts']) {
            // Lock out the IP/username combination
            $lockout_key = 'lockout_' . md5($ip . $username);
            set_transient($lockout_key, time(), self::$config['lockout_duration']);
            
            Redco_Error_Handler::warning(
                "Login lockout triggered for IP: {$ip}, Username: {$username}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'ip' => $ip,
                    'username' => $username,
                    'attempts' => $attempts
                )
            );
        }
        
        Redco_Error_Handler::notice(
            "Failed login attempt #{$attempts} for {$username} from {$ip}",
            Redco_Error_Handler::CONTEXT_SECURITY,
            array(
                'ip' => $ip,
                'username' => $username,
                'attempts' => $attempts
            )
        );
    }
    
    /**
     * Handle successful login
     * 
     * @param string $user_login Username
     * @param WP_User $user User object
     */
    public static function handle_successful_login($user_login, $user) {
        $ip = self::get_client_ip();
        
        // Clear failed attempts on successful login
        $key = 'failed_login_' . md5($ip . $user_login);
        delete_transient($key);
        
        $lockout_key = 'lockout_' . md5($ip . $user_login);
        delete_transient($lockout_key);
        
        Redco_Error_Handler::info(
            "Successful login for {$user_login} from {$ip}",
            Redco_Error_Handler::CONTEXT_SECURITY,
            array(
                'ip' => $ip,
                'username' => $user_login,
                'user_id' => $user->ID
            )
        );
    }
    
    /**
     * Check login attempts before authentication
     * 
     * @param WP_User|WP_Error|null $user User object or error
     * @param string $username Username
     * @param string $password Password
     * @return WP_User|WP_Error User object or error
     */
    public static function check_login_attempts($user, $username, $password) {
        if (empty($username) || empty($password)) {
            return $user;
        }
        
        $ip = self::get_client_ip();
        $lockout_key = 'lockout_' . md5($ip . $username);
        
        if (get_transient($lockout_key)) {
            $error = new WP_Error(
                'login_locked',
                'Too many failed login attempts. Please try again later.'
            );
            
            Redco_Error_Handler::warning(
                "Blocked login attempt during lockout for {$username} from {$ip}",
                Redco_Error_Handler::CONTEXT_SECURITY,
                array(
                    'ip' => $ip,
                    'username' => $username
                )
            );
            
            return $error;
        }
        
        return $user;
    }
    
    /**
     * Admin security check
     */
    public static function admin_security_check() {
        // Check for suspicious admin activity
        if (current_user_can('manage_options')) {
            $suspicious_actions = array(
                'delete_plugins',
                'delete_themes',
                'edit_plugins',
                'edit_themes'
            );
            
            $current_action = $_GET['action'] ?? '';
            
            if (in_array($current_action, $suspicious_actions)) {
                Redco_Error_Handler::warning(
                    "Suspicious admin action: {$current_action}",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $current_action,
                        'user_id' => get_current_user_id(),
                        'ip' => self::get_client_ip()
                    )
                );
            }
        }
    }
    
    /**
     * Filter malicious requests
     */
    public static function filter_malicious_requests() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $query_string = $_SERVER['QUERY_STRING'] ?? '';
        
        // Common attack patterns
        $malicious_patterns = array(
            // SQL injection
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i',
            // XSS
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            // Path traversal
            '/\.\.\//i',
            '/\.\.\\\\/i',
            // File inclusion
            '/php:\/\//i',
            '/data:\/\//i',
            // Command injection
            '/;\s*(cat|ls|pwd|id|whoami|uname)/i'
        );
        
        $suspicious_content = $request_uri . ' ' . $query_string . ' ' . $user_agent;
        
        foreach ($malicious_patterns as $pattern) {
            if (preg_match($pattern, $suspicious_content)) {
                Redco_Error_Handler::critical(
                    "Malicious request detected and blocked",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'pattern' => $pattern,
                        'request_uri' => $request_uri,
                        'user_agent' => $user_agent,
                        'ip' => self::get_client_ip()
                    )
                );
                
                // Block the request
                wp_die('Request blocked for security reasons.', 'Security Error', array('response' => 403));
            }
        }
    }
    
    /**
     * Add security headers
     */
    public static function add_security_headers() {
        if (!headers_sent()) {
            // Prevent clickjacking
            header('X-Frame-Options: SAMEORIGIN');
            
            // Prevent MIME type sniffing
            header('X-Content-Type-Options: nosniff');
            
            // XSS protection
            header('X-XSS-Protection: 1; mode=block');
            
            // Referrer policy
            header('Referrer-Policy: strict-origin-when-cross-origin');
            
            // Content Security Policy (basic)
            if (self::$config['security_level'] === self::LEVEL_HIGH || self::$config['security_level'] === self::LEVEL_STRICT) {
                header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';");
            }
        }
    }
    
    /**
     * Validate AJAX nonce
     */
    public static function validate_ajax_nonce() {
        $action = $_POST['action'] ?? '';
        
        if (strpos($action, 'redco_') === 0) {
            $nonce = $_POST['nonce'] ?? '';
            
            if (!wp_verify_nonce($nonce, $action)) {
                Redco_Error_Handler::warning(
                    "Invalid nonce for AJAX action: {$action}",
                    Redco_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $action,
                        'ip' => self::get_client_ip()
                    )
                );
                
                wp_die('Security check failed.', 'Security Error', array('response' => 403));
            }
        }
    }
    
    /**
     * Scan core files for modifications
     */
    public static function scan_core_files() {
        $core_files = array(
            ABSPATH . 'wp-config.php',
            ABSPATH . 'wp-load.php',
            ABSPATH . 'wp-blog-header.php'
        );
        
        $modified_files = array();
        
        foreach ($core_files as $file) {
            if (file_exists($file)) {
                $current_hash = md5_file($file);
                $stored_hash = get_option('redco_file_hash_' . md5($file));
                
                if ($stored_hash && $stored_hash !== $current_hash) {
                    $modified_files[] = $file;
                } elseif (!$stored_hash) {
                    // Store initial hash
                    update_option('redco_file_hash_' . md5($file), $current_hash);
                }
            }
        }
        
        if (!empty($modified_files)) {
            Redco_Error_Handler::critical(
                "Core files modified: " . implode(', ', $modified_files),
                Redco_Error_Handler::CONTEXT_SECURITY,
                array('modified_files' => $modified_files)
            );
        }
    }
    
    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private static function get_client_ip() {
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Secure wp_die handler
     *
     * @param callable $handler Current handler
     * @return callable Secure handler
     */
    public static function secure_wp_die_handler($handler) {
        return function($message, $title = '', $args = array()) use ($handler) {
            // Sanitize error messages in production
            if (!Redco_Config::is_development_environment()) {
                if (is_string($message) && (
                    strpos($message, 'Fatal error') !== false ||
                    strpos($message, 'Warning') !== false ||
                    strpos($message, 'Notice') !== false
                )) {
                    $message = 'An error occurred. Please contact the administrator.';
                }
            }

            // Ensure handler is callable before using it
            if (is_callable($handler)) {
                return call_user_func($handler, $message, $title, $args);
            }

            // Fallback to default wp_die behavior
            wp_die($message, $title, $args);
        };
    }
    
    /**
     * Load security configuration
     */
    private static function load_config() {
        $config = get_option('redco_optimizer_security_config', array());
        self::$config = wp_parse_args($config, self::$config);
    }
    
    /**
     * Get security statistics
     * 
     * @return array Security statistics
     */
    public static function get_security_stats() {
        global $wpdb;
        
        // Get recent security events from logs
        $recent_events = Redco_Error_Handler::get_recent_logs(Redco_Error_Handler::CONTEXT_SECURITY, 50);
        
        $stats = array(
            'failed_logins_24h' => 0,
            'blocked_requests_24h' => 0,
            'security_level' => self::$config['security_level'],
            'recent_events' => array_slice($recent_events, 0, 10)
        );
        
        // Count events in last 24 hours
        $yesterday = date('Y-m-d H:i:s', strtotime('-24 hours'));
        
        foreach ($recent_events as $event) {
            if ($event['timestamp'] >= $yesterday) {
                if (strpos($event['message'], 'Failed login') !== false) {
                    $stats['failed_logins_24h']++;
                } elseif (strpos($event['message'], 'blocked') !== false) {
                    $stats['blocked_requests_24h']++;
                }
            }
        }
        
        return $stats;
    }
    
    /**
     * Update security configuration
     * 
     * @param array $new_config New configuration
     * @return bool Success
     */
    public static function update_config($new_config) {
        $validated_config = Redco_Settings_Validator::validate_module_settings('security', $new_config);
        self::$config = wp_parse_args($validated_config, self::$config);
        
        return update_option('redco_optimizer_security_config', self::$config);
    }
}

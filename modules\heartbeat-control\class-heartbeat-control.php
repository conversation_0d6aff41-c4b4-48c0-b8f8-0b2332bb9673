<?php
/**
 * Heartbeat Control Module for Redco Optimizer
 *
 * Controls WordPress Heartbeat API frequency to reduce server load.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Heartbeat_Control {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('heartbeat-control')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $this->settings = array(
            'admin_heartbeat' => redco_get_module_option('heartbeat-control', 'admin_heartbeat', 'modify'),
            'admin_frequency' => redco_get_module_option('heartbeat-control', 'admin_frequency', 60),
            'editor_heartbeat' => redco_get_module_option('heartbeat-control', 'editor_heartbeat', 'modify'),
            'editor_frequency' => redco_get_module_option('heartbeat-control', 'editor_frequency', 30),
            'frontend_heartbeat' => redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable'),
            'frontend_frequency' => redco_get_module_option('heartbeat-control', 'frontend_frequency', 60)
        );
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Control heartbeat based on context
        add_action('init', array($this, 'control_heartbeat'));

        // Modify heartbeat frequency
        add_filter('heartbeat_settings', array($this, 'modify_heartbeat_settings'));
    }

    /**
     * Control heartbeat based on settings
     */
    public function control_heartbeat() {
        // Frontend heartbeat control
        if (!is_admin() && $this->settings['frontend_heartbeat'] === 'disable') {
            wp_deregister_script('heartbeat');
        }

        // Admin heartbeat control
        if (is_admin() && !$this->is_post_editor()) {
            if ($this->settings['admin_heartbeat'] === 'disable') {
                wp_deregister_script('heartbeat');
            }
        }

        // Post editor heartbeat control
        if ($this->is_post_editor()) {
            if ($this->settings['editor_heartbeat'] === 'disable') {
                wp_deregister_script('heartbeat');
            }
        }
    }

    /**
     * Modify heartbeat settings
     */
    public function modify_heartbeat_settings($settings) {
        // Frontend frequency
        if (!is_admin() && $this->settings['frontend_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['frontend_frequency'];
        }

        // Admin frequency
        if (is_admin() && !$this->is_post_editor() && $this->settings['admin_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['admin_frequency'];
        }

        // Post editor frequency
        if ($this->is_post_editor() && $this->settings['editor_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['editor_frequency'];
        }

        return $settings;
    }

    /**
     * Check if current page is post editor
     */
    private function is_post_editor() {
        global $pagenow;

        return in_array($pagenow, array('post.php', 'post-new.php'));
    }

    /**
     * Get heartbeat statistics
     */
    public function get_stats() {
        $stats = array(
            'admin_status' => $this->settings['admin_heartbeat'],
            'admin_frequency' => $this->settings['admin_frequency'],
            'editor_status' => $this->settings['editor_heartbeat'],
            'editor_frequency' => $this->settings['editor_frequency'],
            'frontend_status' => $this->settings['frontend_heartbeat'],
            'frontend_frequency' => $this->settings['frontend_frequency']
        );

        return $stats;
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_heartbeat_control() {
    if (redco_is_module_enabled('heartbeat-control')) {
        new Redco_Heartbeat_Control();
    }
}
add_action('init', 'redco_init_heartbeat_control', 10);

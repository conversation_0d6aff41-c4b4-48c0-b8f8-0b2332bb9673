/* Setup Wizard Styles */
.redco-setup-wizard {
    background: #f1f1f1;
    margin: 0;
    padding: 0;
}

.redco-setup-wizard .wrap {
    margin: 0;
    padding: 0;
}

.wizard-container {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.wizard-header {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.wizard-logo {
    margin-bottom: 30px;
}

.wizard-logo .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
}

.wizard-logo h1 {
    color: white;
    font-size: 28px;
    font-weight: 300;
    margin: 0;
}

/* Progress Bar */
.wizard-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 4px;
    border-radius: 2px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    background: white;
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    width: 100%;
    margin-bottom: 10px;
}

.step-indicator {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 12px;
    flex-shrink: 0;
    grid-column: 1 / -1;
}

.step-indicator.active {
    background: white;
    color: #4CAF50;
}

.step-indicator.completed {
    background: white;
    color: #4CAF50;
}

.step-indicator .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.progress-text {
    font-size: 14px;
    opacity: 0.9;
}

/* Content */
.wizard-content {
    padding: 60px 40px;
}

.wizard-step h2 {
    font-size: 32px;
    font-weight: 300;
    margin: 0 0 15px 0;
    text-align: center;
    color: #333;
}

.step-description {
    font-size: 16px;
    color: #666;
    text-align: center;
    margin: 0 0 40px 0;
    line-height: 1.6;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 30px 20px;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.feature-item .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.feature-item h3 {
    font-size: 18px;
    margin: 0 0 10px 0;
    color: #333;
}

.feature-item p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

/* Setup Options */
.setup-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.setup-option {
    border: 2px solid #e1e1e1;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.setup-option:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.setup-option.selected {
    border-color: #4CAF50;
    background: #f8fff8;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.setup-option.selected::after {
    content: '\f147';
    font-family: 'dashicons';
    position: absolute;
    top: 15px;
    right: 15px;
    color: #4CAF50;
    font-size: 20px;
}

.option-header {
    margin-bottom: 15px;
}

.option-header .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #4CAF50;
    margin-bottom: 15px;
}

.option-header h3 {
    font-size: 20px;
    margin: 0;
    color: #333;
}

.setup-option p {
    color: #666;
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.option-details {
    text-align: left;
    background: #f9f9f9;
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
}

.option-details strong {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.option-details ul {
    margin: 0;
    padding-left: 20px;
}

.option-details li {
    color: #666;
    margin-bottom: 4px;
}

/* Completion Message */
.completion-message {
    text-align: center;
    margin-bottom: 40px;
}

.completion-message .dashicons {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.completion-message h2 {
    color: #4CAF50;
    margin-bottom: 10px;
}

/* Next Steps */
.next-steps h3 {
    font-size: 24px;
    margin: 0 0 20px 0;
    text-align: center;
    color: #333;
}

.next-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.next-step-item {
    display: block;
    padding: 25px 20px;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.next-step-item:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
    text-decoration: none;
}

.next-step-item .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #4CAF50;
    margin-bottom: 15px;
}

.next-step-item h4 {
    font-size: 16px;
    margin: 0 0 8px 0;
    color: #333;
}

.next-step-item p {
    color: #666;
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

/* Footer */
.wizard-footer {
    background: #f9f9f9;
    border-top: 1px solid #e1e1e1;
    padding: 30px 40px;
}

.footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-actions-right {
    display: flex;
    gap: 15px;
    align-items: center;
}

.wizard-btn-back,
.wizard-btn-next,
.wizard-btn-setup,
.wizard-btn-finish {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.wizard-btn-skip {
    color: #666;
    text-decoration: none;
    padding: 12px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.wizard-btn-skip:hover {
    color: #333;
    background: #f0f0f0;
    text-decoration: none;
}

/* Loading States */
.wizard-btn-setup.loading {
    opacity: 0.7;
    pointer-events: none;
}

.wizard-btn-setup.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .wizard-container {
        margin: 0;
        box-shadow: none;
    }

    .wizard-header {
        padding: 30px 20px;
    }

    .wizard-content {
        padding: 40px 20px;
    }

    .wizard-footer {
        padding: 20px;
    }

    .footer-actions {
        flex-direction: column;
        gap: 15px;
    }

    .footer-actions-right {
        width: 100%;
        justify-content: center;
    }

    .features-grid,
    .setup-options {
        grid-template-columns: 1fr;
    }

    .next-steps-grid {
        grid-template-columns: 1fr;
    }
}

<?php
/**
 * Progress Tracker Class
 *
 * Handles real-time progress tracking for long-running operations
 *
 * @package RedcoOptimizer
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Progress_Tracker {

    /**
     * Progress data storage
     */
    private static $progress_data = array();

    /**
     * Initialize progress tracker
     */
    public static function init() {
        add_action('wp_ajax_redco_get_progress', array(__CLASS__, 'handle_get_progress'));
        add_action('wp_ajax_redco_clear_page_cache', array(__CLASS__, 'handle_clear_page_cache'));
        add_action('wp_ajax_redco_clear_minified_cache', array(__CLASS__, 'handle_clear_minified_cache'));
        add_action('wp_ajax_redco_clear_cache', array(__CLASS__, 'handle_clear_all_cache'));
        add_action('wp_ajax_redco_database_cleanup', array(__CLASS__, 'handle_database_cleanup'));
        add_action('wp_ajax_redco_preload_cache', array(__CLASS__, 'handle_preload_cache'));

        // Debug handlers
        add_action('wp_ajax_redco_debug_cache_info', array(__CLASS__, 'handle_debug_cache_info'));
        add_action('wp_ajax_redco_create_test_cache', array(__CLASS__, 'handle_create_test_cache'));

        // Critical Resource Optimizer handlers
        add_action('wp_ajax_redco_optimize_critical_resources', array(__CLASS__, 'handle_optimize_critical_resources'));
        add_action('wp_ajax_redco_clear_critical_cache', array(__CLASS__, 'handle_clear_critical_cache'));
        add_action('wp_ajax_redco_generate_critical_css', array(__CLASS__, 'handle_generate_critical_css'));

        // Debug handlers for module persistence
        add_action('wp_ajax_redco_debug_module_state', array(__CLASS__, 'handle_debug_module_state'));
        add_action('wp_ajax_redco_test_module_toggle', array(__CLASS__, 'handle_test_module_toggle'));
    }

    /**
     * Start progress tracking for a session
     */
    public static function start_session($session_id, $total_steps = 100) {
        self::$progress_data[$session_id] = array(
            'percentage' => 0,
            'current_operation' => 'Initializing...',
            'operation_details' => 'Preparing to start operation',
            'stats' => array(
                'files_processed' => 0,
                'items_cleaned' => 0,
                'space_saved' => 0
            ),
            'completed' => false,
            'error' => false,
            'message' => '',
            'total_steps' => $total_steps,
            'current_step' => 0,
            'start_time' => time()
        );

        // Store in transient for persistence across requests
        set_transient('redco_progress_' . $session_id, self::$progress_data[$session_id], 300); // 5 minutes
    }

    /**
     * Update progress for a session
     */
    public static function update_progress($session_id, $data) {
        if (!isset(self::$progress_data[$session_id])) {
            // Try to load from transient
            $stored_data = get_transient('redco_progress_' . $session_id);
            if ($stored_data) {
                self::$progress_data[$session_id] = $stored_data;
            } else {
                return false;
            }
        }

        // Update progress data
        self::$progress_data[$session_id] = array_merge(self::$progress_data[$session_id], $data);

        // Store updated data in transient
        set_transient('redco_progress_' . $session_id, self::$progress_data[$session_id], 300);

        return true;
    }

    /**
     * Complete progress session
     */
    public static function complete_session($session_id, $message = '', $stats = array()) {
        self::update_progress($session_id, array(
            'percentage' => 100,
            'current_operation' => 'Completed Successfully',
            'operation_details' => $message ?: 'Operation completed successfully',
            'completed' => true,
            'stats' => array_merge(
                isset(self::$progress_data[$session_id]['stats']) ? self::$progress_data[$session_id]['stats'] : array(),
                $stats
            ),
            'message' => $message
        ));
    }

    /**
     * Set error for progress session
     */
    public static function set_error($session_id, $error_message) {
        self::update_progress($session_id, array(
            'error' => true,
            'message' => $error_message,
            'current_operation' => 'Error Occurred',
            'operation_details' => $error_message
        ));
    }

    /**
     * Get progress data for a session
     */
    public static function get_progress($session_id) {
        if (isset(self::$progress_data[$session_id])) {
            return self::$progress_data[$session_id];
        }

        // Try to load from transient
        $stored_data = get_transient('redco_progress_' . $session_id);
        if ($stored_data) {
            self::$progress_data[$session_id] = $stored_data;
            return $stored_data;
        }

        return false;
    }

    /**
     * Clean up old progress sessions
     */
    public static function cleanup_old_sessions() {
        global $wpdb;

        // Clean up transients older than 5 minutes
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options}
             WHERE option_name LIKE %s
             AND option_name LIKE %s",
            '_transient_timeout_redco_progress_%',
            '%'
        ));
    }

    /**
     * Handle AJAX request for progress updates
     */
    public static function handle_get_progress() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        if (empty($session_id)) {
            wp_send_json_error(array('message' => 'Invalid session ID'));
        }

        $progress = self::get_progress($session_id);

        if ($progress === false) {
            wp_send_json_error(array('message' => 'Progress session not found'));
        }

        wp_send_json_success($progress);
    }

    /**
     * Handle clear page cache with progress tracking
     */
    public static function handle_clear_page_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        // This ensures the operation starts right away
        do_action('redco_process_clear_page_cache', $session_id);

        // Also schedule as backup (in case immediate execution fails)
        wp_schedule_single_event(time() + 1, 'redco_process_clear_page_cache', array($session_id));

        wp_send_json_success(array(
            'message' => 'Page cache clearing started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle clear minified cache with progress tracking
     */
    public static function handle_clear_minified_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_clear_minified_cache', $session_id);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_clear_minified_cache', array($session_id));

        wp_send_json_success(array(
            'message' => 'Minified cache clearing started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle clear all cache with progress tracking
     */
    public static function handle_clear_all_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_clear_all_cache', $session_id);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_clear_all_cache', array($session_id));

        wp_send_json_success(array(
            'message' => 'Cache clearing started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle database cleanup with progress tracking
     */
    public static function handle_database_cleanup() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);
        $options = isset($_POST['options']) ? $_POST['options'] : array();

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_database_cleanup', $session_id, $options);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_database_cleanup', array($session_id, $options));

        wp_send_json_success(array(
            'message' => 'Database cleanup started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle cache preloading with progress tracking
     */
    public static function handle_preload_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_preload_cache', $session_id);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_preload_cache', array($session_id));

        wp_send_json_success(array(
            'message' => 'Cache preloading started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle debug cache info request
     */
    public static function handle_debug_cache_info() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $upload_dir = wp_upload_dir();
        $cache_base_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';

        $cache_info = array(
            'base_directory' => $cache_base_dir,
            'directories' => array(),
            'total_files' => 0,
            'total_size' => 0
        );

        $cache_types = array('page-cache', 'minified', 'css', 'js', 'images');

        foreach ($cache_types as $type) {
            $dir_path = $cache_base_dir . $type . '/';
            $dir_info = array(
                'path' => $dir_path,
                'exists' => is_dir($dir_path),
                'files' => 0,
                'size' => 0
            );

            if ($dir_info['exists']) {
                $files = self::get_files_recursive_debug($dir_path);
                $dir_info['files'] = count($files);

                foreach ($files as $file) {
                    if (is_file($file)) {
                        $file_size = filesize($file);
                        $dir_info['size'] += $file_size;
                        $cache_info['total_size'] += $file_size;
                    }
                }

                $cache_info['total_files'] += $dir_info['files'];
            }

            $cache_info['directories'][$type] = $dir_info;
        }

        wp_send_json_success($cache_info);
    }

    /**
     * Handle create test cache files request
     */
    public static function handle_create_test_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/page-cache/';

        // Ensure directory exists
        if (!is_dir($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }

        // Create test files
        $test_files = array(
            'homepage.html' => '<html><head><title>Homepage Cache</title></head><body><h1>Cached Homepage</h1><p>This is a cached version of the homepage.</p></body></html>',
            'about-page.html' => '<html><head><title>About Page Cache</title></head><body><h1>Cached About Page</h1><p>This is a cached version of the about page.</p></body></html>',
            'contact-page.html' => '<html><head><title>Contact Page Cache</title></head><body><h1>Cached Contact Page</h1><p>This is a cached version of the contact page.</p></body></html>'
        );

        $created_files = 0;
        $total_size = 0;

        foreach ($test_files as $filename => $content) {
            $file_path = $cache_dir . $filename;
            if (file_put_contents($file_path, $content)) {
                $created_files++;
                $total_size += strlen($content);

                // Set realistic file modification time
                $random_time = time() - rand(0, 86400);
                touch($file_path, $random_time);
            }
        }

        wp_send_json_success(array(
            'files_created' => $created_files,
            'total_size' => $total_size,
            'directory' => $cache_dir
        ));
    }

    /**
     * Get files recursively for debugging (simpler version)
     */
    private static function get_files_recursive_debug($dir) {
        $files = array();

        if (!is_dir($dir)) {
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Handle critical resource optimization request
     */
    public static function handle_optimize_critical_resources() {
        try {
            error_log('Redco Debug: Critical resource optimization AJAX handler called');
            error_log('Redco Debug: POST data: ' . print_r($_POST, true));

            // Verify nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
                error_log('Redco Debug: Nonce verification failed');
                wp_send_json_error(array('message' => 'Security check failed'));
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                error_log('Redco Debug: User lacks manage_options capability');
                wp_send_json_error(array('message' => 'Insufficient permissions'));
            }

            // Generate unique session ID
            $session_id = uniqid('redco_critical_');
            error_log('Redco Debug: Generated session ID: ' . $session_id);

            // Initialize progress tracking
            self::start_session($session_id, 5);
            error_log('Redco Debug: Progress tracking initialized');

            // Check if processing hook is registered
            $has_hook = has_action('redco_process_optimize_critical_resources');
            error_log('Redco Debug: Processing hook registered: ' . ($has_hook ? 'Yes' : 'No'));

            // Execute immediately instead of relying on cron
            do_action('redco_process_optimize_critical_resources', $session_id);
            error_log('Redco Debug: do_action called for immediate processing');

            // Also schedule as backup
            wp_schedule_single_event(time() + 1, 'redco_process_optimize_critical_resources', array($session_id));
            error_log('Redco Debug: Backup cron scheduled');

            error_log('Redco Debug: Sending success response');
            wp_send_json_success(array(
                'message' => 'Critical resource optimization started',
                'session_id' => $session_id
            ));

        } catch (Exception $e) {
            error_log('Redco Debug: Exception in AJAX handler: ' . $e->getMessage());
            error_log('Redco Debug: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Server error: ' . $e->getMessage()));
        } catch (Error $e) {
            error_log('Redco Debug: Fatal error in AJAX handler: ' . $e->getMessage());
            error_log('Redco Debug: Error trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Fatal error: ' . $e->getMessage()));
        }
    }

    /**
     * Handle clear critical cache request with progress tracking
     */
    public static function handle_clear_critical_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_clear_critical_cache', $session_id);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_clear_critical_cache', array($session_id));

        wp_send_json_success(array(
            'message' => 'Critical cache clearing started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle generate critical CSS request with progress tracking
     */
    public static function handle_generate_critical_css() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        $session_id = sanitize_text_field($_POST['session_id']);

        // Start progress tracking
        self::start_session($session_id);

        // Execute immediately instead of relying on cron
        do_action('redco_process_generate_critical_css', $session_id);

        // Also schedule as backup
        wp_schedule_single_event(time() + 1, 'redco_process_generate_critical_css', array($session_id));

        wp_send_json_success(array(
            'message' => 'Critical CSS generation started',
            'session_id' => $session_id
        ));
    }

    /**
     * Handle debug module state request
     */
    public static function handle_debug_module_state() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        try {
            // Get current options from database
            $options = get_option('redco_optimizer_options', array());
            $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();

            // Check specific module
            $module = sanitize_text_field($_POST['module'] ?? 'critical-resource-optimizer');
            $is_enabled = in_array($module, $enabled_modules);

            // Get raw database value for debugging
            global $wpdb;
            $raw_option = $wpdb->get_var($wpdb->prepare(
                "SELECT option_value FROM {$wpdb->options} WHERE option_name = %s",
                'redco_optimizer_options'
            ));

            wp_send_json_success(array(
                'module' => $module,
                'is_enabled' => $is_enabled,
                'enabled_modules' => $enabled_modules,
                'all_options' => $options,
                'raw_database_value' => $raw_option,
                'database_check' => !empty($raw_option),
                'modules_enabled_exists' => isset($options['modules_enabled']),
                'modules_enabled_count' => count($enabled_modules),
                'timestamp' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to debug module state: ' . $e->getMessage()
            ));
        }
    }

    /**
     * Handle test module toggle request
     */
    public static function handle_test_module_toggle() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
        }

        try {
            $module = sanitize_text_field($_POST['module'] ?? 'critical-resource-optimizer');
            $action = sanitize_text_field($_POST['test_action'] ?? 'enable'); // enable or disable

            // Get current state
            $options = get_option('redco_optimizer_options', array());
            $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
            $was_enabled = in_array($module, $enabled_modules);

            // Perform the action
            if ($action === 'enable') {
                if (!in_array($module, $enabled_modules)) {
                    $enabled_modules[] = $module;
                }
            } else {
                $enabled_modules = array_diff($enabled_modules, array($module));
            }

            // Update options
            $options['modules_enabled'] = $enabled_modules;
            $update_result = update_option('redco_optimizer_options', $options);

            // Verify the update
            $verification_options = get_option('redco_optimizer_options', array());
            $verification_enabled_modules = isset($verification_options['modules_enabled']) ? $verification_options['modules_enabled'] : array();
            $is_now_enabled = in_array($module, $verification_enabled_modules);

            wp_send_json_success(array(
                'module' => $module,
                'action' => $action,
                'was_enabled' => $was_enabled,
                'is_now_enabled' => $is_now_enabled,
                'update_result' => $update_result,
                'enabled_modules_before' => $enabled_modules,
                'enabled_modules_after' => $verification_enabled_modules,
                'verification_passed' => ($action === 'enable') ? $is_now_enabled : !$is_now_enabled,
                'timestamp' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to test module toggle: ' . $e->getMessage()
            ));
        }
    }
}

// Initialize progress tracker
Redco_Progress_Tracker::init();

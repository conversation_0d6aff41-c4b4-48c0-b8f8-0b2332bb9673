/**
 * Enhanced UI Styles for Redco Optimizer
 * Sidebar Navigation & Setup Wizard Improvements
 */

/* ===== ENHANCED SIDEBAR NAVIGATION ===== */

/* Progress Section */
.nav-progress-section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.optimization-progress {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.progress-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #32373c;
}

.progress-score {
    font-size: 18px;
    font-weight: bold;
    color: #4CAF50;
}

.progress-bar-container {
    position: relative;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50 0%, #7dd3a0 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 16px;
    font-weight: bold;
    color: #32373c;
    line-height: 1;
}

.stat-label {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

/* Setup Reminder */
.setup-reminder {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.setup-reminder-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.setup-reminder .dashicons {
    color: #856404;
    font-size: 20px;
    margin-top: 2px;
}

.reminder-text strong {
    display: block;
    font-size: 13px;
    color: #856404;
    margin-bottom: 4px;
}

.reminder-text p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.setup-btn {
    display: inline-block;
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: background 0.2s ease;
}

.setup-btn:hover {
    background: #45a049;
    color: white;
}

/* Navigation Menu */
.nav-menu-section {
    flex: 1;
    padding: 0;
}

.nav-section-header {
    padding: 15px 20px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f1;
    background: #fafafa;
}

.section-title {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-count {
    background: #4CAF50;
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* Enhanced Navigation Items */
.nav-item {
    position: relative;
    border-bottom: 1px solid #f0f0f1;
}

.nav-item.active {
    background: linear-gradient(90deg, #4CAF50 0%, #7dd3a0 100%);
}

.nav-item.active .nav-link {
    color: white;
}

.nav-item.active .nav-description {
    color: rgba(255, 255, 255, 0.8);
}

.nav-item.active .indicator-arrow {
    color: white;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    text-decoration: none;
    color: #32373c;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.nav-icon {
    position: relative;
    margin-right: 12px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon .dashicons {
    font-size: 18px;
}

.module-status {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid white;
}

.module-status.active {
    background: #4CAF50;
}

.nav-content {
    flex: 1;
    min-width: 0;
}

.nav-title {
    display: block;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 2px;
}

.nav-description {
    display: block;
    font-size: 11px;
    color: #666;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-indicator {
    margin-left: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.nav-item:hover .nav-indicator,
.nav-item.active .nav-indicator {
    opacity: 1;
}

.indicator-arrow {
    font-size: 12px;
    color: #4CAF50;
}

/* Footer Actions */
.nav-footer-section {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background: #fafafa;
}

.quick-actions-footer {
    display: flex;
    gap: 10px;
}

.footer-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 8px;
    text-decoration: none;
    color: #666;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 11px;
}

.footer-action:hover {
    background: #4CAF50;
    color: white;
    text-decoration: none;
}

.footer-action .dashicons {
    font-size: 16px;
    margin-bottom: 4px;
}

.action-text {
    font-weight: 500;
}

/* ===== ENHANCED SETUP WIZARD ===== */

/* Wizard Container */
.redco-setup-wizard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.redco-wizard-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Enhanced Header */
.wizard-branding {
    text-align: center;
    margin-bottom: 40px;
}

.wizard-logo-container {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    backdrop-filter: blur(10px);
}

.wizard-icon {
    font-size: 40px;
    color: white;
}

.wizard-branding h1 {
    color: white;
    font-size: 32px;
    font-weight: 300;
    margin: 0 0 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.wizard-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    margin: 0;
    font-weight: 300;
}

/* Enhanced Progress Bar */
.wizard-progress-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.progress-bar-wrapper {
    position: relative;
}

.progress-bar-track {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-bottom: 25px;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50 0%, #7dd3a0 100%);
    border-radius: 2px;
    transition: width 0.5s ease;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.progress-steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    width: 100%;
    margin-bottom: 20px;
}

.progress-step-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    position: relative;
    grid-column: 1 / -1;
    padding: 12px 16px;
    background: var(--redco-light, #f8f9fa);
    border-radius: 10px;
    border: 1px solid var(--redco-border, #e9ecef);
    box-sizing: border-box;
}

.progress-step {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.progress-step.pending {
    background: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.7);
}

.progress-step.active {
    background: #4CAF50;
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    transform: scale(1.1);
}

.progress-step.completed {
    background: #7dd3a0;
    color: white;
}

.step-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1.2); opacity: 0; }
}

.step-label {
    text-align: left;
    color: #333;
    flex: 1;
}

.step-title {
    display: block;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 4px;
}

.step-description {
    display: block;
    font-size: 11px;
    opacity: 0.8;
    line-height: 1.3;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.progress-info {
    display: flex;
    gap: 15px;
}

.progress-text,
.progress-percentage {
    font-size: 13px;
    font-weight: 500;
}

.estimated-time {
    font-size: 12px;
    opacity: 0.8;
    font-style: italic;
}

/* Enhanced Wizard Footer */
.wizard-footer {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
    backdrop-filter: blur(10px);
}

.wizard-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.nav-left,
.nav-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.step-dots {
    display: flex;
    gap: 8px;
}

.step-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.step-dot.pending {
    background: rgba(255, 255, 255, 0.3);
}

.step-dot.active {
    background: #4CAF50;
    transform: scale(1.2);
}

.step-dot.completed {
    background: #7dd3a0;
}

/* Enhanced Wizard Buttons */
.wizard-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.wizard-btn-primary {
    background: #4CAF50;
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.wizard-btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.wizard-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.wizard-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.wizard-btn-success {
    background: linear-gradient(135deg, #4CAF50 0%, #7dd3a0 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.wizard-btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.wizard-btn-ghost {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.wizard-btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
    width: 0;
    transition: width 0.3s ease;
}

.wizard-btn:hover .btn-progress {
    width: 100%;
}

.btn-text {
    font-weight: 500;
}

/* Progress Summary */
.wizard-progress-summary {
    text-align: center;
    color: white;
}

.progress-summary-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-text {
    font-size: 14px;
    font-weight: 500;
}

.step-help {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 12px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 6px;
    margin: 0 auto;
    max-width: 400px;
}

.step-help .dashicons {
    font-size: 14px;
}

/* Wizard Content */
.wizard-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
}

/* Setup Options Enhancement */
.setup-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.setup-option {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #fafafa;
}

.setup-option:hover {
    border-color: #4CAF50;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
}

.setup-option.selected {
    border-color: #4CAF50;
    background: #f8fff8;
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
}

.setup-option .dashicons {
    font-size: 32px;
    color: #4CAF50;
    margin-bottom: 15px;
}

.setup-option h3 {
    margin: 0 0 10px;
    color: #32373c;
    font-size: 18px;
}

.setup-option p {
    margin: 0 0 15px;
    color: #666;
    line-height: 1.5;
}

.setup-details {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.setup-details strong {
    display: block;
    margin-bottom: 8px;
    color: #32373c;
    font-size: 13px;
}

.setup-details ul {
    margin: 0;
    padding-left: 20px;
}

.setup-details li {
    margin-bottom: 4px;
    color: #666;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wizard-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .nav-left,
    .nav-right {
        width: 100%;
        justify-content: center;
    }

    .progress-steps {
        gap: 6px;
    }

    .progress-step-wrapper {
        width: 100%;
        padding: 10px 12px;
    }

    .setup-options {
        grid-template-columns: 1fr;
    }

    .quick-actions-footer {
        flex-direction: column;
        gap: 8px;
    }

    .footer-action {
        flex-direction: row;
        justify-content: center;
        gap: 8px;
    }
}

/**
 * Simplified Settings Layout CSS
 * Redco Optimizer Plugin
 * Card-free design with left-aligned controls and optimized widths
 */

/* CSS Variables */
:root {
    --redco-primary: #4CAF50;
    --redco-border: #e2e8f0;
    --redco-text: #374151;
    --redco-text-light: #6b7280;
    --redco-bg: #ffffff;
    --redco-bg-light: #f9fafb;
    --redco-transition: all 0.2s ease;
    --redco-divider: #f1f5f9;
    --redco-radius: 6px;
}

/* CARD-FREE LAYOUT WITH BRAND COLORS */

/* Settings Page Container - White background with brand theme */
.redco-optimizer-settings {
    background: #ffffff !important;
    min-height: 100vh;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

/* Settings Header - Clean with brand colors */
.redco-settings-header {
    background: #ffffff !important;
    border: none !important;
    border-bottom: none !important;
    padding: 32px !important;
    margin-bottom: 0 !important;
    box-shadow: none !important;
}

.redco-settings-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
}

.redco-settings-header p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 16px;
}

/* Navigation Tabs - With top and bottom borders */
.redco-nav-tab-wrapper {
    background: #ffffff !important;
    border-top: 1px solid var(--redco-border) !important;
    border-bottom: 1px solid var(--redco-border) !important;
    padding: 0 32px !important;
    display: flex;
    gap: 0;
    margin: 0 !important;
    box-shadow: none !important;
    position: relative;
    z-index: 1;
}

.redco-nav-tab {
    padding: 16px 24px;
    background: transparent !important;
    border: none !important;
    border-bottom: 3px solid transparent !important;
    color: var(--redco-text-light);
    text-decoration: none;
    font-weight: 400;
    transition: var(--redco-transition);
    cursor: pointer;
    box-shadow: none !important;
}

.redco-nav-tab:hover {
    color: var(--redco-text);
    background: var(--redco-bg-light) !important;
    border: none !important;
    box-shadow: none !important;
}

/* Active tab styling - using correct class name */
.redco-nav-tab.redco-nav-tab-active,
.redco-nav-tab.nav-tab-active {
    color: var(--redco-primary) !important;
    background: #ffffff !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
    position: relative;
    z-index: 2;
    font-weight: 700 !important;
    text-decoration: underline !important;
    text-decoration-color: var(--redco-primary) !important;
    text-decoration-thickness: 2px !important;
    text-underline-offset: 4px !important;
}

/* Settings Content - White background, full width */
.redco-settings-content {
    padding: 32px;
    margin: 0;
    background: #ffffff !important;
    border: none !important;
    box-shadow: none !important;
}

/* Settings Section - Clean with horizontal line below header */
.redco-settings-section {
    margin-bottom: 48px;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.settings-section-header {
    margin-bottom: 32px;
    background: transparent !important;
    border: none !important;
    border-bottom: 1px solid var(--redco-border) !important;
    box-shadow: none !important;
    padding: 0 0 16px 0 !important;
}

.settings-section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
}

.settings-section-header p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
}

/* FORCE REMOVE ALL CARD STYLING - Override any other CSS files */
.settings-card,
.settings-cards-grid,
.settings-card-header,
.settings-card-content,
.redco-card,
.card-header,
.setup-wizard-card,
.wizard-card-content {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Remove any container backgrounds or borders */
.redco-optimizer-settings .settings-card,
.redco-optimizer-settings .settings-cards-grid,
.redco-optimizer-settings .redco-card {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Remove hover effects on cards */
.settings-card:hover,
.redco-card:hover {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* Settings Form - Completely transparent */
.redco-settings-form {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Setting Items - Left-aligned controls with horizontal dividers */
.setting-item {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    padding: 24px 0;
    border-bottom: 1px solid var(--redco-divider);
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item:first-child {
    padding-top: 0;
}

/* Control on the left */
.setting-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    order: 1;
}

/* Info text on the right */
.setting-info {
    flex: 1;
    min-width: 0;
    order: 2;
}

.setting-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 6px 0;
    line-height: 1.4;
}

.setting-info p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Hide extra elements for clean design */
.setting-warning,
.setting-benefits,
.benefit-item,
.setting-recommendation,
.setting-note {
    display: none !important;
}

/* Checkbox - Brand colored with proper styling */
.redco-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
}

.redco-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.checkbox-custom {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border: 2px solid var(--redco-border);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--redco-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkbox-custom:before {
    content: "";
    width: 10px;
    height: 6px;
    border: 2px solid #ffffff;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    opacity: 0;
    transition: var(--redco-transition);
}

.redco-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background-color: var(--redco-primary);
    border-color: var(--redco-primary);
}

.redco-checkbox input[type="checkbox"]:checked + .checkbox-custom:before {
    opacity: 1;
}

.redco-checkbox input[type="checkbox"]:focus + .checkbox-custom {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.redco-checkbox input[type="checkbox"]:hover + .checkbox-custom {
    border-color: var(--redco-primary);
}

/* Form Controls - Optimized widths */
.redco-select {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    min-width: 180px;
    max-width: 300px;
}

.redco-text-input {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    width: 300px;
    max-width: 100%;
}

.redco-number-input {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    width: 120px;
}

.redco-select:focus,
.redco-text-input:focus,
.redco-number-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Auto-save feedback */
.setting-item.saving {
    opacity: 0.7;
}

.setting-item.saved {
    background: rgba(76, 175, 80, 0.05);
    border-radius: var(--redco-radius);
    margin: 0 -16px;
    padding-left: 16px;
    padding-right: 16px;
}

.setting-item.error {
    background: rgba(239, 68, 68, 0.05);
    border-radius: var(--redco-radius);
    margin: 0 -16px;
    padding-left: 16px;
    padding-right: 16px;
}

/* Role Selection Grid */
.role-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    max-width: 500px;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.role-checkbox-item input[type="checkbox"] {
    margin: 0;
}

.role-checkbox-item label {
    font-size: 14px;
    color: var(--redco-text);
    cursor: pointer;
}

/* Form Footer - Remove borders */
.settings-form-footer {
    margin-top: 32px;
    padding-top: 24px;
    border: none !important;
    border-top: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.redco-save-button {
    background: var(--redco-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--redco-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--redco-transition);
}

.redco-save-button:hover {
    background: var(--redco-primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-settings-header,
    .redco-nav-tab-wrapper,
    .redco-settings-content {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .setting-item {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .setting-control {
        align-self: flex-start;
        order: 2;
    }
    
    .setting-info {
        order: 1;
    }
    
    .redco-text-input {
        width: 100%;
    }
    
    .redco-nav-tab-wrapper {
        flex-wrap: wrap;
    }
}

/* Hide WordPress default form table */
.redco-settings-form table.form-table {
    display: none;
}

/* OVERRIDE ANY WORDPRESS ADMIN STYLING */
.wrap .redco-optimizer-settings,
.wrap .redco-settings-section {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin-bottom: 48px !important;
}

/* Settings section header - preserve the border-bottom */
.wrap .settings-section-header {
    background: transparent !important;
    box-shadow: none !important;
    padding: 0 0 16px 0 !important;
    margin-bottom: 32px !important;
    border: none !important;
    border-bottom: 1px solid var(--redco-border) !important;
}

/* Remove any WordPress notice styling conflicts */
.wrap .notice + .redco-optimizer-settings,
.wrap .updated + .redco-optimizer-settings,
.wrap .error + .redco-optimizer-settings {
    margin-top: 0 !important;
    background: transparent !important;
    border: none !important;
}

/* Ensure completely flat layout */
.redco-optimizer-settings *:not(.setting-item):not(.redco-toggle-switch):not(.toggle-slider):not(.redco-select):not(.redco-text-input):not(.redco-number-input):not(.redco-save-button) {
    border-radius: 0 !important;
}

/* Remove any remaining container styling */
.redco-optimizer-settings .wrap,
.redco-optimizer-settings .postbox,
.redco-optimizer-settings .inside {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* Brand Color for All Dashicons */
.redco-optimizer-settings .dashicons,
.redco-optimizer-settings .dashicons-before:before,
.redco-nav-tab .dashicons,
.redco-settings-header .dashicons,
.settings-section-header .dashicons,
.wizard-info .dashicons,
.button .dashicons {
    color: var(--redco-primary) !important;
}

/* Specific dashicon styling for tabs */
.redco-nav-tab .dashicons {
    margin-right: 8px;
    font-size: 16px;
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

/* Header dashicons */
.redco-settings-header .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

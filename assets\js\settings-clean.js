/**
 * Clean Enterprise Settings JavaScript
 * Redco Optimizer Plugin
 * Built from scratch for enterprise-grade functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Auto-save configuration
    const SAVE_DELAY = 1000; // 1 second delay
    let saveTimeout;

    /**
     * Initialize settings functionality
     */
    function initSettings() {
        initAutoSave();
        initFormControls();
    }

    /**
     * Initialize auto-save functionality
     */
    function initAutoSave() {
        // Handle toggle switches
        $('.settings-toggle').on('change', function() {
            const $toggle = $(this);
            const settingGroup = $toggle.data('setting-group');
            const settingName = $toggle.data('setting-name');
            const value = $toggle.is(':checked') ? 1 : 0;

            autoSaveSetting(settingGroup, settingName, value, $toggle);
        });

        // Handle select dropdowns
        $('.redco-select').on('change', function() {
            const $select = $(this);
            const name = $select.attr('name');
            const value = $select.val();

            if (name && name.includes('[') && name.includes(']')) {
                const matches = name.match(/^([^[]+)\[([^]]+)\]$/);
                if (matches) {
                    const settingGroup = matches[1];
                    const settingName = matches[2];
                    autoSaveSetting(settingGroup, settingName, value, $select);
                }
            }
        });

        // Handle text inputs
        $('.redco-text-input, .redco-number-input').on('input', function() {
            const $input = $(this);
            const name = $input.attr('name');
            const value = $input.val();

            // Clear existing timeout
            clearTimeout(saveTimeout);

            // Add visual feedback
            $input.closest('.setting-item').addClass('saving');

            // Save after delay
            saveTimeout = setTimeout(function() {
                if (name && name.includes('[') && name.includes(']')) {
                    const matches = name.match(/^([^[]+)\[([^]]+)\]$/);
                    if (matches) {
                        const settingGroup = matches[1];
                        const settingName = matches[2];
                        autoSaveSetting(settingGroup, settingName, value, $input);
                    }
                }
            }, SAVE_DELAY);
        });
    }

    /**
     * Auto-save a setting
     */
    function autoSaveSetting(settingGroup, settingName, value, $element) {
        const $settingItem = $element.closest('.setting-item');

        // Add saving state
        $settingItem.addClass('saving').removeClass('saved error');

        // Prepare data
        const data = {
            action: 'redco_save_setting',
            setting_group: settingGroup,
            setting_name: settingName,
            value: value,
            nonce: redco_settings.nonce
        };

        // Make AJAX request
        $.ajax({
            url: redco_settings.ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    // Show success state
                    $settingItem.removeClass('saving').addClass('saved');
                    
                    // Remove success state after 2 seconds
                    setTimeout(function() {
                        $settingItem.removeClass('saved');
                    }, 2000);
                } else {
                    // Show error state
                    $settingItem.removeClass('saving').addClass('error');
                    console.error('Save failed:', response.data);
                    
                    // Remove error state after 3 seconds
                    setTimeout(function() {
                        $settingItem.removeClass('error');
                    }, 3000);
                }
            },
            error: function(xhr, status, error) {
                // Show error state
                $settingItem.removeClass('saving').addClass('error');
                console.error('AJAX error:', error);
                
                // Remove error state after 3 seconds
                setTimeout(function() {
                    $settingItem.removeClass('error');
                }, 3000);
            }
        });
    }

    /**
     * Initialize form controls
     */
    function initFormControls() {
        // Prevent form submission since we auto-save
        $('.redco-settings-form').on('submit', function(e) {
            e.preventDefault();
            return false;
        });

        // Hide any save buttons
        $('.redco-save-button, .settings-form-footer').hide();
    }

    /**
     * Initialize everything
     */
    initSettings();
});

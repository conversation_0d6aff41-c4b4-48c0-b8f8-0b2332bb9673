<?php
/**
 * Error Handling and Logging System for Redco Optimizer
 * 
 * Centralized error handling with different log levels and contexts
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Error_Handler {
    
    /**
     * Log levels
     */
    const LOG_EMERGENCY = 'emergency';
    const LOG_ALERT = 'alert';
    const LOG_CRITICAL = 'critical';
    const LOG_ERROR = 'error';
    const LOG_WARNING = 'warning';
    const LOG_NOTICE = 'notice';
    const LOG_INFO = 'info';
    const LOG_DEBUG = 'debug';
    
    /**
     * Log contexts
     */
    const CONTEXT_WEBP = 'webp';
    const CONTEXT_CACHE = 'cache';
    const CONTEXT_API = 'api';
    const CONTEXT_SETTINGS = 'settings';
    const CONTEXT_SECURITY = 'security';
    const CONTEXT_PERFORMANCE = 'performance';
    const CONTEXT_GENERAL = 'general';
    
    /**
     * Maximum log file size (5MB)
     */
    const MAX_LOG_SIZE = 5242880;
    
    /**
     * Maximum number of log files to keep
     */
    const MAX_LOG_FILES = 5;
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Log directory
     */
    private $log_dir;
    
    /**
     * Whether logging is enabled
     */
    private $logging_enabled;
    
    /**
     * Minimum log level
     */
    private $min_log_level;
    
    /**
     * Log level priorities
     */
    private static $log_priorities = array(
        self::LOG_EMERGENCY => 0,
        self::LOG_ALERT => 1,
        self::LOG_CRITICAL => 2,
        self::LOG_ERROR => 3,
        self::LOG_WARNING => 4,
        self::LOG_NOTICE => 5,
        self::LOG_INFO => 6,
        self::LOG_DEBUG => 7
    );
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->log_dir = Redco_Config::get_cache_dir('logs');
        $this->logging_enabled = $this->should_enable_logging();
        $this->min_log_level = $this->get_min_log_level();
        
        // Create log directory if it doesn't exist
        if ($this->logging_enabled && !file_exists($this->log_dir)) {
            wp_mkdir_p($this->log_dir);
            $this->create_htaccess();
        }
    }
    
    /**
     * Log a message
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param string $context Log context
     * @param array $data Additional data
     */
    public static function log($level, $message, $context = self::CONTEXT_GENERAL, $data = array()) {
        $instance = self::get_instance();
        $instance->write_log($level, $message, $context, $data);
    }
    
    /**
     * Log emergency message
     */
    public static function emergency($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_EMERGENCY, $message, $context, $data);
    }
    
    /**
     * Log alert message
     */
    public static function alert($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_ALERT, $message, $context, $data);
    }
    
    /**
     * Log critical message
     */
    public static function critical($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_CRITICAL, $message, $context, $data);
    }
    
    /**
     * Log error message
     */
    public static function error($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_ERROR, $message, $context, $data);
    }
    
    /**
     * Log warning message
     */
    public static function warning($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_WARNING, $message, $context, $data);
    }
    
    /**
     * Log notice message
     */
    public static function notice($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_NOTICE, $message, $context, $data);
    }
    
    /**
     * Log info message
     */
    public static function info($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_INFO, $message, $context, $data);
    }
    
    /**
     * Log debug message
     */
    public static function debug($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_DEBUG, $message, $context, $data);
    }
    
    /**
     * Write log entry
     */
    private function write_log($level, $message, $context, $data) {
        if (!$this->logging_enabled) {
            return;
        }
        
        // Check if log level meets minimum threshold
        if (!$this->should_log_level($level)) {
            return;
        }
        
        $log_file = $this->get_log_file($context);
        
        // Rotate log if it's too large
        $this->rotate_log_if_needed($log_file);
        
        $log_entry = $this->format_log_entry($level, $message, $context, $data);
        
        // Write to log file
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
        
        // Also log critical errors to WordPress error log
        if (in_array($level, array(self::LOG_EMERGENCY, self::LOG_ALERT, self::LOG_CRITICAL, self::LOG_ERROR))) {
            error_log("Redco Optimizer [{$level}]: {$message}");
        }
    }
    
    /**
     * Format log entry
     */
    private function format_log_entry($level, $message, $context, $data) {
        $timestamp = current_time('Y-m-d H:i:s');
        $level_upper = strtoupper($level);
        $context_upper = strtoupper($context);
        
        $entry = "[{$timestamp}] {$level_upper} [{$context_upper}] {$message}";
        
        if (!empty($data)) {
            $entry .= ' | Data: ' . json_encode($data);
        }
        
        $entry .= PHP_EOL;
        
        return $entry;
    }
    
    /**
     * Get log file path for context
     */
    private function get_log_file($context) {
        $filename = "redco-{$context}.log";
        return $this->log_dir . '/' . $filename;
    }
    
    /**
     * Check if logging should be enabled
     */
    private function should_enable_logging() {
        // Enable logging in development environments
        if (Redco_Config::is_development_environment()) {
            return true;
        }
        
        // Enable logging if explicitly enabled
        if (defined('REDCO_ENABLE_LOGGING') && REDCO_ENABLE_LOGGING) {
            return true;
        }
        
        // Enable logging for critical errors only in production
        return get_option('redco_optimizer_enable_error_logging', false);
    }
    
    /**
     * Get minimum log level
     */
    private function get_min_log_level() {
        if (Redco_Config::is_development_environment()) {
            return self::LOG_DEBUG;
        }
        
        return get_option('redco_optimizer_min_log_level', self::LOG_ERROR);
    }
    
    /**
     * Check if log level should be logged
     */
    private function should_log_level($level) {
        $level_priority = isset(self::$log_priorities[$level]) ? self::$log_priorities[$level] : 7;
        $min_priority = isset(self::$log_priorities[$this->min_log_level]) ? self::$log_priorities[$this->min_log_level] : 3;
        
        return $level_priority <= $min_priority;
    }
    
    /**
     * Rotate log file if needed
     */
    private function rotate_log_if_needed($log_file) {
        if (!file_exists($log_file)) {
            return;
        }
        
        if (filesize($log_file) < self::MAX_LOG_SIZE) {
            return;
        }
        
        // Rotate logs
        for ($i = self::MAX_LOG_FILES - 1; $i > 0; $i--) {
            $old_file = $log_file . '.' . $i;
            $new_file = $log_file . '.' . ($i + 1);
            
            if (file_exists($old_file)) {
                if ($i === self::MAX_LOG_FILES - 1) {
                    unlink($old_file); // Delete oldest log
                } else {
                    rename($old_file, $new_file);
                }
            }
        }
        
        // Move current log to .1
        rename($log_file, $log_file . '.1');
    }
    
    /**
     * Create .htaccess file for log directory
     */
    private function create_htaccess() {
        $htaccess_file = $this->log_dir . '/.htaccess';
        
        if (file_exists($htaccess_file)) {
            return;
        }
        
        $htaccess_content = "# Redco Optimizer Log Directory\n";
        $htaccess_content .= "Options -Indexes\n";
        $htaccess_content .= "deny from all\n";
        
        file_put_contents($htaccess_file, $htaccess_content);
    }
    
    /**
     * Get recent log entries
     * 
     * @param string $context Log context
     * @param int $limit Number of entries to retrieve
     * @return array Log entries
     */
    public static function get_recent_logs($context = self::CONTEXT_GENERAL, $limit = 50) {
        $instance = self::get_instance();
        $log_file = $instance->get_log_file($context);
        
        if (!file_exists($log_file)) {
            return array();
        }
        
        $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        if (!$lines) {
            return array();
        }
        
        // Get last N lines
        $recent_lines = array_slice($lines, -$limit);
        
        $entries = array();
        foreach ($recent_lines as $line) {
            $entries[] = $instance->parse_log_entry($line);
        }
        
        return array_reverse($entries); // Most recent first
    }
    
    /**
     * Parse log entry
     */
    private function parse_log_entry($line) {
        $pattern = '/^\[([^\]]+)\] (\w+) \[([^\]]+)\] (.+)$/';
        
        if (preg_match($pattern, $line, $matches)) {
            return array(
                'timestamp' => $matches[1],
                'level' => strtolower($matches[2]),
                'context' => strtolower($matches[3]),
                'message' => $matches[4]
            );
        }
        
        return array(
            'timestamp' => '',
            'level' => 'unknown',
            'context' => 'unknown',
            'message' => $line
        );
    }
    
    /**
     * Clear logs for a context
     * 
     * @param string $context Log context
     * @return bool Success
     */
    public static function clear_logs($context = null) {
        $instance = self::get_instance();
        
        if ($context) {
            $log_file = $instance->get_log_file($context);
            return file_exists($log_file) ? unlink($log_file) : true;
        }
        
        // Clear all logs
        $log_files = glob($instance->log_dir . '/redco-*.log*');
        $success = true;
        
        foreach ($log_files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Get log file size
     * 
     * @param string $context Log context
     * @return int File size in bytes
     */
    public static function get_log_size($context = null) {
        $instance = self::get_instance();
        
        if ($context) {
            $log_file = $instance->get_log_file($context);
            return file_exists($log_file) ? filesize($log_file) : 0;
        }
        
        // Get total size of all logs
        $log_files = glob($instance->log_dir . '/redco-*.log*');
        $total_size = 0;
        
        foreach ($log_files as $file) {
            $total_size += filesize($file);
        }
        
        return $total_size;
    }
}

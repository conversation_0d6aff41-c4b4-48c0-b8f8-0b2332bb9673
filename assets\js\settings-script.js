/**
 * Redco Optimizer Settings Auto-Save
 * Minimal and clean auto-save functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Auto-save functionality
    let saveTimeout;
    const SAVE_DELAY = 1000; // 1 second delay

    // Auto-save on toggle change
    $('.settings-toggle').on('change', function() {
        const $toggle = $(this);
        const settingGroup = $toggle.data('setting-group');
        const settingName = $toggle.data('setting-name');
        const value = $toggle.is(':checked') ? 1 : 0;

        // Clear existing timeout
        clearTimeout(saveTimeout);

        // Add visual feedback
        $toggle.closest('.setting-item').addClass('saving');

        // Save after delay
        saveTimeout = setTimeout(function() {
            autoSaveSetting(settingGroup, settingName, value, $toggle);
        }, SAVE_DELAY);
    });

    // Auto-save on input change
    $('.redco-text-input, .redco-number-input, .redco-select').on('input change', function() {
        const $input = $(this);
        const settingGroup = $input.attr('name').match(/\[(.*?)\]/)[0].replace(/[\[\]]/g, '');
        const settingName = $input.attr('name').match(/\[(.*?)\]$/)[1];
        const value = $input.val();

        // Clear existing timeout
        clearTimeout(saveTimeout);

        // Add visual feedback
        $input.closest('.setting-item').addClass('saving');

        // Save after delay
        saveTimeout = setTimeout(function() {
            autoSaveSetting(settingGroup, settingName, value, $input);
        }, SAVE_DELAY);
    });

    // Auto-save function
    function autoSaveSetting(group, name, value, $element) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                if (response.success) {
                    // Show success feedback
                    $element.closest('.setting-item').removeClass('saving').addClass('saved');
                    
                    // Remove feedback after 2 seconds
                    setTimeout(function() {
                        $element.closest('.setting-item').removeClass('saved');
                    }, 2000);
                } else {
                    // Show error feedback
                    $element.closest('.setting-item').removeClass('saving').addClass('error');
                    
                    // Remove feedback after 3 seconds
                    setTimeout(function() {
                        $element.closest('.setting-item').removeClass('error');
                    }, 3000);
                }
            },
            error: function() {
                // Show error feedback
                $element.closest('.setting-item').removeClass('saving').addClass('error');
                
                // Remove feedback after 3 seconds
                setTimeout(function() {
                    $element.closest('.setting-item').removeClass('error');
                }, 3000);
            }
        });
    }

    // Settings page uses server-side navigation, not client-side tabs
    // Remove the tab switching JavaScript that was preventing normal navigation
});

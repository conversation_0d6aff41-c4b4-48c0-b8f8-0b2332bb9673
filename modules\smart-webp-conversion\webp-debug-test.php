<?php
/**
 * WebP Conversion Debug Test
 * 
 * This file helps debug WebP conversion issues
 * Access via: yoursite.com/wp-content/plugins/redco-optimizer/modules/smart-webp-conversion/webp-debug-test.php?test=1
 */

// Prevent direct access without test parameter
if (!isset($_GET['test']) || $_GET['test'] !== '1') {
    exit('Access denied. Add ?test=1 to run debug test.');
}

// Load WordPress
$wp_load_paths = array(
    '../../../../../wp-load.php',
    '../../../../wp-load.php',
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    exit('Could not load WordPress. Please check file paths.');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    exit('Access denied. Admin privileges required.');
}

echo "<h1>🔧 WebP Conversion Debug Test</h1>\n";

// Test 1: Check if WebP module is enabled
echo "<h2>1. Module Status</h2>\n";
if (function_exists('redco_is_module_enabled')) {
    $module_enabled = redco_is_module_enabled('smart-webp-conversion');
    echo $module_enabled ? "✅ WebP module is enabled\n" : "❌ WebP module is disabled\n";
} else {
    echo "❌ redco_is_module_enabled function not found\n";
}

// Test 2: Check server WebP support
echo "<h2>2. Server WebP Support</h2>\n";
if (function_exists('imagewebp')) {
    echo "✅ imagewebp() function available\n";
} else {
    echo "❌ imagewebp() function not available\n";
}

if (imagetypes() & IMG_WEBP) {
    echo "✅ WebP support enabled in GD library\n";
} else {
    echo "❌ WebP support not enabled in GD library\n";
}

// Test 3: Check class loading
echo "<h2>3. Class Loading</h2>\n";
if (class_exists('Redco_Smart_WebP_Conversion')) {
    echo "✅ Redco_Smart_WebP_Conversion class loaded\n";
} else {
    echo "❌ Redco_Smart_WebP_Conversion class not loaded\n";
    
    // Try to load it
    $class_file = __DIR__ . '/class-smart-webp-conversion.php';
    if (file_exists($class_file)) {
        require_once $class_file;
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            echo "✅ Successfully loaded Redco_Smart_WebP_Conversion class\n";
        } else {
            echo "❌ Failed to load Redco_Smart_WebP_Conversion class\n";
        }
    } else {
        echo "❌ Class file not found: $class_file\n";
    }
}

// Test 4: Check Performance Monitor
echo "<h2>4. Performance Monitor</h2>\n";
if (class_exists('Redco_Performance_Monitor')) {
    echo "✅ Redco_Performance_Monitor class available\n";
    
    try {
        Redco_Performance_Monitor::start_timer('test_timer');
        $time = Redco_Performance_Monitor::end_timer('test_timer');
        echo "✅ Performance Monitor working correctly (test time: {$time}s)\n";
    } catch (Exception $e) {
        echo "❌ Performance Monitor error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Redco_Performance_Monitor class not available\n";
}

// Test 5: Check helper functions
echo "<h2>5. Helper Functions</h2>\n";
if (function_exists('redco_safe_get_attached_file')) {
    echo "✅ redco_safe_get_attached_file function available\n";
} else {
    echo "❌ redco_safe_get_attached_file function not available\n";
}

// Test 6: Test AJAX nonce generation
echo "<h2>6. AJAX Nonce Test</h2>\n";
$nonce = wp_create_nonce('redco_webp_bulk_convert');
if ($nonce) {
    echo "✅ AJAX nonce generated successfully: " . substr($nonce, 0, 10) . "...\n";
    
    // Test nonce verification
    if (wp_verify_nonce($nonce, 'redco_webp_bulk_convert')) {
        echo "✅ Nonce verification working\n";
    } else {
        echo "❌ Nonce verification failed\n";
    }
} else {
    echo "❌ Failed to generate AJAX nonce\n";
}

// Test 7: Check database connectivity
echo "<h2>7. Database Test</h2>\n";
global $wpdb;
try {
    $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment'");
    echo "✅ Database query successful. Found $test_query attachments\n";
} catch (Exception $e) {
    echo "❌ Database query failed: " . $e->getMessage() . "\n";
}

// Test 8: Test WebP instance creation
echo "<h2>8. WebP Instance Test</h2>\n";
if (class_exists('Redco_Smart_WebP_Conversion')) {
    try {
        $webp_instance = new Redco_Smart_WebP_Conversion();
        echo "✅ WebP instance created successfully\n";
        
        // Test method existence
        $required_methods = array(
            'ajax_enhanced_bulk_convert',
            'get_cached_initial_total',
            'get_images_by_ids',
            'real_webp_conversion'
        );
        
        foreach ($required_methods as $method) {
            if (method_exists($webp_instance, $method)) {
                echo "✅ Method $method exists\n";
            } else {
                echo "❌ Method $method missing\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ WebP instance creation failed: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "❌ Fatal error creating WebP instance: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Cannot test WebP instance - class not available\n";
}

// Test 9: Check WordPress media functions
echo "<h2>9. WordPress Media Functions</h2>\n";
$media_functions = array(
    'wp_get_attachment_metadata',
    'get_attached_file',
    'wp_upload_dir',
    'update_post_meta'
);

foreach ($media_functions as $function) {
    if (function_exists($function)) {
        echo "✅ Function $function available\n";
    } else {
        echo "❌ Function $function not available\n";
    }
}

// Test 10: Memory and execution limits
echo "<h2>10. System Limits</h2>\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "s\n";
echo "Current Memory Usage: " . size_format(memory_get_usage(true)) . "\n";
echo "Peak Memory Usage: " . size_format(memory_get_peak_usage(true)) . "\n";

echo "<h2>🎯 Debug Test Complete</h2>\n";
echo "<p>If all tests show ✅, the WebP conversion should work properly.</p>\n";
echo "<p>If you see ❌ errors, those need to be fixed before WebP conversion will work.</p>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>Fix any ❌ errors shown above</li>\n";
echo "<li>Try the WebP conversion again</li>\n";
echo "<li>Check browser console for JavaScript errors</li>\n";
echo "<li>Check WordPress error logs for PHP errors</li>\n";
echo "</ul>\n";
?>

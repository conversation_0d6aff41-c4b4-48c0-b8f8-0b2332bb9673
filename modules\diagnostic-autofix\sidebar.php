<?php
/**
 * Diagnostic & Auto-Fix Module Sidebar
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module statistics
$diagnostic_module = new Redco_Diagnostic_AutoFix();
$stats = $diagnostic_module->get_stats();
$last_scan = get_option('redco_diagnostic_results', array());
$fix_history = get_option('redco_diagnostic_fix_history', array());
?>

<div class="redco-sidebar">
    <!-- Diagnostic Statistics -->
    <div class="redco-sidebar-section">
        <h3>
            <span class="dashicons dashicons-chart-bar"></span>
            <?php _e('Diagnostic Statistics', 'redco-optimizer'); ?>
        </h3>
        <div class="sidebar-stats">
            <div class="stat-item">
                <div class="stat-label"><?php _e('Health Score', 'redco-optimizer'); ?></div>
                <div class="stat-value health-score">
                    <div class="score-circle <?php echo $stats['health_score'] >= 80 ? 'good' : ($stats['health_score'] >= 60 ? 'warning' : 'critical'); ?>">
                        <?php echo $stats['health_score']; ?>%
                    </div>
                </div>
            </div>

            <div class="stat-item">
                <div class="stat-label"><?php _e('Performance Score', 'redco-optimizer'); ?></div>
                <div class="stat-value performance-score">
                    <div class="score-circle <?php echo $stats['performance_score'] >= 80 ? 'good' : ($stats['performance_score'] >= 60 ? 'warning' : 'critical'); ?>">
                        <?php echo $stats['performance_score']; ?>%
                    </div>
                </div>
            </div>

            <div class="stat-row">
                <div class="stat-label"><?php _e('Total Issues', 'redco-optimizer'); ?></div>
                <div class="stat-value <?php echo $stats['issues_found'] == 0 ? 'good' : 'warning'; ?>">
                    <?php echo $stats['issues_found']; ?>
                </div>
            </div>

            <div class="stat-row">
                <div class="stat-label"><?php _e('Critical Issues', 'redco-optimizer'); ?></div>
                <div class="stat-value <?php echo $stats['critical_issues'] == 0 ? 'good' : 'critical'; ?>">
                    <?php echo $stats['critical_issues']; ?>
                </div>
            </div>

            <div class="stat-row">
                <div class="stat-label"><?php _e('Auto-Fixable', 'redco-optimizer'); ?></div>
                <div class="stat-value <?php echo $stats['auto_fixable_issues'] > 0 ? 'warning' : 'good'; ?>">
                    <?php echo $stats['auto_fixable_issues']; ?>
                </div>
            </div>

            <div class="stat-row">
                <div class="stat-label"><?php _e('Fixes Applied', 'redco-optimizer'); ?></div>
                <div class="stat-value">
                    <?php echo $stats['fixes_applied']; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Scan Information -->
    <div class="redco-sidebar-section">
        <h3>
            <span class="dashicons dashicons-search"></span>
            <?php _e('Scan Information', 'redco-optimizer'); ?>
        </h3>
        <div class="scan-info">
            <?php if ($stats['last_scan_time'] > 0): ?>
            <div class="info-item">
                <div class="info-label"><?php _e('Last Scan', 'redco-optimizer'); ?></div>
                <div class="info-value">
                    <?php echo human_time_diff($stats['last_scan_time']) . ' ' . __('ago', 'redco-optimizer'); ?>
                </div>
            </div>

            <?php if (isset($last_scan['scan_duration'])): ?>
            <div class="info-item">
                <div class="info-label"><?php _e('Scan Duration', 'redco-optimizer'); ?></div>
                <div class="info-value">
                    <?php echo $last_scan['scan_duration']; ?>ms
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($last_scan['scan_type'])): ?>
            <div class="info-item">
                <div class="info-label"><?php _e('Scan Type', 'redco-optimizer'); ?></div>
                <div class="info-value">
                    <?php echo esc_html(ucfirst($last_scan['scan_type'])); ?>
                </div>
            </div>
            <?php endif; ?>
            <?php else: ?>
            <div class="no-scan-message">
                <p><?php _e('No scans performed yet.', 'redco-optimizer'); ?></p>
                <button type="button" class="button button-primary button-small" id="sidebar-quick-scan">
                    <?php _e('Run Quick Scan', 'redco-optimizer'); ?>
                </button>
            </div>
            <?php endif; ?>

            <div class="info-item">
                <div class="info-label"><?php _e('Auto-Scan Frequency', 'redco-optimizer'); ?></div>
                <div class="info-value">
                    <?php echo esc_html(ucfirst($stats['scan_frequency'])); ?>
                </div>
            </div>

            <div class="info-item">
                <div class="info-label"><?php _e('Auto-Fix', 'redco-optimizer'); ?></div>
                <div class="info-value">
                    <span class="status-indicator <?php echo $stats['auto_fix_enabled'] ? 'enabled' : 'disabled'; ?>">
                        <?php echo $stats['auto_fix_enabled'] ? __('Enabled', 'redco-optimizer') : __('Disabled', 'redco-optimizer'); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Issue Categories -->
    <?php if (!empty($last_scan['issues'])): ?>
    <div class="redco-sidebar-section">
        <h3>
            <span class="dashicons dashicons-category"></span>
            <?php _e('Issue Categories', 'redco-optimizer'); ?>
        </h3>
        <div class="issue-categories">
            <?php
            $categories = array();
            foreach ($last_scan['issues'] as $issue) {
                $category = $issue['category'];
                if (!isset($categories[$category])) {
                    $categories[$category] = array('total' => 0, 'critical' => 0, 'auto_fixable' => 0);
                }
                $categories[$category]['total']++;
                if ($issue['severity'] === 'critical') {
                    $categories[$category]['critical']++;
                }
                if ($issue['auto_fixable']) {
                    $categories[$category]['auto_fixable']++;
                }
            }

            foreach ($categories as $category => $data):
            ?>
            <div class="category-item">
                <div class="category-header">
                    <div class="category-name"><?php echo esc_html(ucfirst($category)); ?></div>
                    <div class="category-count"><?php echo $data['total']; ?></div>
                </div>
                <div class="category-details">
                    <?php if ($data['critical'] > 0): ?>
                    <span class="detail-item critical">
                        <?php echo sprintf(__('%d Critical', 'redco-optimizer'), $data['critical']); ?>
                    </span>
                    <?php endif; ?>
                    <?php if ($data['auto_fixable'] > 0): ?>
                    <span class="detail-item fixable">
                        <?php echo sprintf(__('%d Auto-Fixable', 'redco-optimizer'), $data['auto_fixable']); ?>
                    </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Emergency Mode -->
    <div class="redco-sidebar-section">
        <h3>
            <span class="dashicons dashicons-warning"></span>
            <?php _e('Emergency Mode', 'redco-optimizer'); ?>
        </h3>
        <div class="emergency-mode-info">
            <div class="emergency-status">
                <div class="status-indicator <?php echo $stats['emergency_mode_active'] ? 'active' : 'inactive'; ?>">
                    <?php if ($stats['emergency_mode_active']): ?>
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Active', 'redco-optimizer'); ?>
                    <?php else: ?>
                        <span class="dashicons dashicons-yes"></span>
                        <?php _e('Inactive', 'redco-optimizer'); ?>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($stats['emergency_mode_active']): ?>
            <div class="emergency-message">
                <p><?php _e('Emergency mode is currently active. Heavy features are disabled to improve performance.', 'redco-optimizer'); ?></p>
                <button type="button" class="button button-secondary button-small" id="deactivate-emergency-mode">
                    <?php _e('Deactivate', 'redco-optimizer'); ?>
                </button>
            </div>
            <?php else: ?>
            <div class="emergency-trigger">
                <p><?php _e('Automatically activates when performance drops below threshold.', 'redco-optimizer'); ?></p>
                <button type="button" class="button button-secondary button-small" id="activate-emergency-mode">
                    <?php _e('Activate Now', 'redco-optimizer'); ?>
                </button>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Fixes -->
    <div class="redco-sidebar-section recent-fixes-section">
        <h3>
            <span class="dashicons dashicons-admin-tools"></span>
            <?php _e('Recent Fixes', 'redco-optimizer'); ?>
            <button type="button" class="refresh-recent-fixes" id="refresh-recent-fixes">
                <span class="dashicons dashicons-update"></span>
            </button>
        </h3>
        <div class="recent-fixes" id="recent-fixes-container">
            <div class="recent-fixes-loading">
                <span class="dashicons dashicons-update"></span>
                <?php _e('Loading recent fixes...', 'redco-optimizer'); ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="redco-sidebar-section">
        <h3>
            <span class="dashicons dashicons-admin-generic"></span>
            <?php _e('Quick Actions', 'redco-optimizer'); ?>
        </h3>
        <div class="sidebar-actions">
            <button type="button" class="button button-primary button-small full-width" id="sidebar-comprehensive-scan">
                <span class="dashicons dashicons-search"></span>
                <?php _e('Comprehensive Scan', 'redco-optimizer'); ?>
            </button>

            <button type="button" class="button button-secondary button-small full-width" id="sidebar-wordpress-scan">
                <span class="dashicons dashicons-wordpress"></span>
                <?php _e('WordPress Scan', 'redco-optimizer'); ?>
            </button>

            <?php if ($stats['auto_fixable_issues'] > 0): ?>
            <button type="button" class="button button-secondary button-small full-width" id="sidebar-apply-fixes">
                <span class="dashicons dashicons-admin-tools"></span>
                <?php _e('Apply Auto-Fixes', 'redco-optimizer'); ?>
            </button>
            <?php endif; ?>

            <button type="button" class="button button-secondary button-small full-width" id="sidebar-export-report">
                <span class="dashicons dashicons-download"></span>
                <?php _e('Export Report', 'redco-optimizer'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin: 0 auto;
}

.score-circle.good {
    background: #E8F5E8;
    color: #4CAF50;
    border: 2px solid #4CAF50;
}

.score-circle.warning {
    background: #FFF3E0;
    color: #FF9800;
    border: 2px solid #FF9800;
}

.score-circle.critical {
    background: #FFEBEE;
    color: #F44336;
    border: 2px solid #F44336;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-value.good { color: #4CAF50; }
.stat-value.warning { color: #FF9800; }
.stat-value.critical { color: #F44336; }

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #666;
}

.status-indicator {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.enabled,
.status-indicator.active {
    background: #E8F5E8;
    color: #4CAF50;
}

.status-indicator.disabled,
.status-indicator.inactive {
    background: #F5F5F5;
    color: #666;
}

.category-item {
    margin-bottom: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.category-name {
    font-weight: 500;
}

.category-count {
    background: #4CAF50;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
}

.category-details {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.detail-item {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
}

.detail-item.critical {
    background: #FFEBEE;
    color: #F44336;
}

.detail-item.fixable {
    background: #E8F5E8;
    color: #4CAF50;
}

.emergency-status {
    text-align: center;
    margin-bottom: 10px;
}

.emergency-status .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
}

.emergency-message,
.emergency-trigger {
    text-align: center;
}

.emergency-message p,
.emergency-trigger p {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.fix-item {
    margin-bottom: 10px;
    padding: 8px;
    background: #f9f9f9;
    border-radius: 4px;
    font-size: 12px;
}

.fix-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.fix-date {
    color: #666;
}

.fix-count {
    font-weight: 500;
}

.fix-backup {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
}

.fix-backup .dashicons {
    font-size: 14px;
}

.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.full-width {
    width: 100%;
    justify-content: center;
    display: flex;
    align-items: center;
    gap: 5px;
}

.no-scan-message {
    text-align: center;
    padding: 15px;
    color: #666;
}

.no-scan-message p {
    margin-bottom: 10px;
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner .dashicons {
    animation: spin 1s linear infinite;
}

.refresh-recent-fixes {
    text-decoration: none !important;
    color: #666 !important;
}

.refresh-recent-fixes:hover {
    color: #4CAF50 !important;
}

.no-fixes-message {
    text-align: center;
    padding: 15px;
    color: #666;
    font-style: italic;
}
</style>

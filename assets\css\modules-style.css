/**
 * Redco Optimizer Modules Page Styles
 * Modern, clean, and professional styling for the modules interface
 */

/* WordPress Admin Notice Spacing for Modules */
.redco-optimizer-modules {
    clear: both;
    margin-top: 20px;
}

.wrap .notice + .redco-optimizer-modules,
.wrap .updated + .redco-optimizer-modules,
.wrap .error + .redco-optimizer-modules {
    margin-top: 10px;
}

/* Modules Page Header */
.redco-optimizer-modules .redco-modules-header {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    margin: 0 -20px 30px -2px;
    padding: 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    min-height: 182px;
}

.redco-modules-header .modules-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 40px 20px 50px;
}

.redco-modules-header h1 {
    color: white;
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-modules-header h1 .dashicons {
    font-size: 32px;
}

.redco-modules-header .modules-subtitle {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

.redco-modules-header .modules-actions {
    display: flex;
    gap: 12px;
}

.redco-modules-header .modules-actions .button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-modules-header .modules-actions .button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.redco-modules-header .modules-actions .button-primary {
    background: rgba(255, 255, 255, 0.9);
    color: #388E3C;
}

.redco-modules-header .modules-actions .button-primary:hover {
    background: white;
    color: #2E7D32;
}

/* Modules Container */
.redco-modules-container {
    max-width: 100%;
    margin: 0;
}

/* Modules Navigation Tabs */
.redco-modules-nav-wrapper {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    border: none;
    background: none;
    flex-wrap: wrap;
}

.redco-modules-nav-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s ease;
    min-width: 180px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.redco-modules-nav-tab:hover {
    border-color: #4CAF50;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.redco-modules-nav-tab.redco-modules-nav-tab-active {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    border-color: #388E3C;
    color: white;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.redco-modules-nav-tab.redco-modules-nav-tab-active .tab-description {
    color: white !important;
}

.redco-modules-nav-tab .dashicons {
    font-size: 20px;
    flex-shrink: 0;
}

.redco-modules-nav-tab .tab-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.redco-modules-nav-tab .tab-title {
    font-weight: 600;
    font-size: 16px;
}

.redco-modules-nav-tab .tab-description {
    font-size: 12px;
    opacity: 0.8;
}

/* Modules Content */
.redco-modules-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.modules-content-wrapper {
    padding: 40px;
}

/* Modules Sections */
.modules-section {
    margin-bottom: 40px;
}

.modules-section:last-child {
    margin-bottom: 0;
}

.modules-section-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.modules-section-header h2 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.modules-section-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Modules Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
}

/* Module Cards */
.module-card {
    background: #fafafa;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.module-card:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.1);
    transform: translateY(-2px);
}

.module-card.enabled {
    border-color: #e2e8f0;
    background: #f8fafc;
}

.module-card.coming-soon {
    border-color: #e2e8f0;
    background: #f8fafc;
    opacity: 0.7;
}

.module-card.premium {
    border-color: #e2e8f0;
    background: #f8fafc;
}

/* Module Card Header */
.module-card-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 16px;
    background: #f8f9fa;
}

.module-card.enabled .module-card-header {
    background: #f8f9fa;
}

.module-card.coming-soon .module-card-header {
    background: #f8f9fa;
}

.module-card.premium .module-card-header {
    background: #f8f9fa;
}

.module-icon {
    width: 48px;
    height: 48px;
    background: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.module-icon .dashicons {
    font-size: 24px;
    color: #64748b;
}

.module-card.coming-soon .module-icon .dashicons {
    color: #64748b;
}

.module-card.premium .module-icon .dashicons {
    color: #64748b;
}

.module-title {
    flex: 1;
}

.module-title h3 {
    margin: 0 0 4px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.module-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-type-badge.free {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.module-type-badge.premium {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.module-toggle {
    flex-shrink: 0;
}



.coming-soon-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: #f1f5f9;
    color: #64748b;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

/* Module Card Content */
.module-card-content {
    padding: 20px;
}

.module-description {
    margin: 0 0 16px 0;
    color: #666;
    line-height: 1.5;
}

.module-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
}

.module-status.active {
    background: #f1f5f9;
    color: #64748b;
}

.module-status.inactive {
    background: #f1f5f9;
    color: #64748b;
}

.coming-soon-features ul {
    margin: 0;
    padding-left: 20px;
    color: #666;
}

.coming-soon-features li {
    margin-bottom: 4px;
}

/* Module Card Footer */
.module-card-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.module-configure-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
}

.module-upgrade-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
    background: #64748b !important;
    border: none !important;
    color: white !important;
}

.module-enable-hint {
    text-align: center;
    color: #64748b;
    font-size: 14px;
    font-style: italic;
}

.coming-soon-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

/* Empty State */
.modules-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state-icon {
    margin-bottom: 20px;
}

.empty-state-icon .dashicons {
    font-size: 64px;
    color: #ccc;
}

.modules-empty-state h3 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 24px;
}

.modules-empty-state p {
    margin: 0 0 24px 0;
    font-size: 16px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-modules-header .modules-header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .redco-modules-nav-wrapper {
        flex-direction: column;
    }

    .redco-modules-nav-tab {
        min-width: auto;
    }

    .modules-grid {
        grid-template-columns: 1fr;
    }

    .modules-content-wrapper {
        padding: 20px;
    }

    .module-card-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .module-title {
        text-align: center;
    }
}

/* Loading States */
.modules-grid.loading {
    opacity: 0.6;
    pointer-events: none;
}

.module-card.loading {
    position: relative;
}

.module-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.module-card.success {
    border-color: #4CAF50;
    background: #e8f5e8;
}

.module-card.error {
    border-color: #f44336;
    background: #ffebee;
}

/* Hover Effects for Interactive Elements */
.module-configure-btn:hover {
    background: #4CAF50 !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.module-upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

/* Module Card Animations */
.module-card {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for multiple cards */
.module-card:nth-child(1) { animation-delay: 0.1s; }
.module-card:nth-child(2) { animation-delay: 0.2s; }
.module-card:nth-child(3) { animation-delay: 0.3s; }
.module-card:nth-child(4) { animation-delay: 0.4s; }
.module-card:nth-child(5) { animation-delay: 0.5s; }
.module-card:nth-child(6) { animation-delay: 0.6s; }

/* Focus States for Accessibility */
.redco-modules-nav-tab:focus,
.module-configure-btn:focus,
.module-upgrade-btn:focus {
    outline: 3px solid rgba(76, 175, 80, 0.3);
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .redco-modules-content {
        background: #1e1e1e;
        color: #e0e0e0;
    }

    .module-card {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }

    .module-card-header {
        background: #2d2d2d;
    }

    .module-card-footer {
        background: #2d2d2d;
    }

    .module-title h3 {
        color: #e0e0e0;
    }

    .module-description {
        color: #b0b0b0;
    }

    .modules-section-header h2 {
        color: #e0e0e0;
    }

    .modules-section-header p {
        color: #b0b0b0;
    }
}

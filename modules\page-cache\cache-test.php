<?php
/**
 * Page Cache Performance Test
 * 
 * This file helps test and verify the cache improvements
 * Run this from WordPress admin or via WP-CLI to test cache performance
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Cache_Performance_Test {
    
    private $page_cache;
    
    public function __construct() {
        if (class_exists('Redco_Page_Cache')) {
            $this->page_cache = new Redco_Page_Cache();
        }
    }
    
    /**
     * Run comprehensive cache tests
     */
    public function run_tests() {
        $results = array();
        
        echo "<h2>🔧 Redco Page Cache Performance Test</h2>\n";
        
        // Test 1: Cache Key Generation
        $results['cache_key'] = $this->test_cache_key_generation();
        
        // Test 2: File Storage
        $results['file_storage'] = $this->test_file_storage();
        
        // Test 3: Cache Statistics
        $results['statistics'] = $this->test_cache_statistics();
        
        // Test 4: Performance Metrics
        $results['performance'] = $this->test_performance_metrics();
        
        // Summary
        $this->display_test_summary($results);
        
        return $results;
    }
    
    /**
     * Test cache key generation consistency
     */
    private function test_cache_key_generation() {
        echo "<h3>🔑 Testing Cache Key Generation</h3>\n";
        
        if (!$this->page_cache) {
            echo "❌ Page Cache class not available\n";
            return false;
        }
        
        // Simulate different scenarios
        $_SERVER['REQUEST_URI'] = '/test-page/';
        
        // Test 1: Same URL should generate same key
        $key1 = $this->get_cache_key_via_reflection();
        $key2 = $this->get_cache_key_via_reflection();
        
        if ($key1 === $key2) {
            echo "✅ Same URL generates consistent cache key\n";
        } else {
            echo "❌ Cache key inconsistency detected\n";
            return false;
        }
        
        // Test 2: Different URLs should generate different keys
        $_SERVER['REQUEST_URI'] = '/different-page/';
        $key3 = $this->get_cache_key_via_reflection();
        
        if ($key1 !== $key3) {
            echo "✅ Different URLs generate different cache keys\n";
        } else {
            echo "❌ Different URLs generating same cache key\n";
            return false;
        }
        
        echo "✅ Cache key generation test passed\n\n";
        return true;
    }
    
    /**
     * Test file-based cache storage
     */
    private function test_file_storage() {
        echo "<h3>💾 Testing File-Based Cache Storage</h3>\n";
        
        $cache_dir = redco_get_cache_dir() . 'page-cache/';
        
        // Check if cache directory exists or can be created
        if (!is_dir($cache_dir)) {
            if (!wp_mkdir_p($cache_dir)) {
                echo "❌ Cannot create cache directory: $cache_dir\n";
                return false;
            }
        }
        
        if (!is_writable($cache_dir)) {
            echo "❌ Cache directory is not writable: $cache_dir\n";
            return false;
        }
        
        echo "✅ Cache directory is accessible and writable\n";
        echo "📁 Cache directory: $cache_dir\n\n";
        return true;
    }
    
    /**
     * Test cache statistics
     */
    private function test_cache_statistics() {
        echo "<h3>📊 Testing Cache Statistics</h3>\n";
        
        if (!$this->page_cache) {
            echo "❌ Page Cache class not available\n";
            return false;
        }
        
        $stats = $this->page_cache->get_cache_stats();
        
        if (!is_array($stats)) {
            echo "❌ Cache statistics not available\n";
            return false;
        }
        
        $required_keys = array('cache_hits', 'cache_misses', 'hit_ratio', 'cached_pages');
        foreach ($required_keys as $key) {
            if (!isset($stats[$key])) {
                echo "❌ Missing required statistic: $key\n";
                return false;
            }
        }
        
        echo "✅ Cache statistics structure is valid\n";
        echo "📈 Current Stats:\n";
        echo "   - Cache Hits: " . number_format($stats['cache_hits']) . "\n";
        echo "   - Cache Misses: " . number_format($stats['cache_misses']) . "\n";
        echo "   - Hit Ratio: " . $stats['hit_ratio'] . "%\n";
        echo "   - Cached Pages: " . number_format($stats['cached_pages']) . "\n\n";
        
        return true;
    }
    
    /**
     * Test performance metrics
     */
    private function test_performance_metrics() {
        echo "<h3>⚡ Testing Performance Metrics</h3>\n";
        
        if (!$this->page_cache) {
            echo "❌ Page Cache class not available\n";
            return false;
        }
        
        $stats = $this->page_cache->get_cache_stats();
        
        // Check performance status
        if (isset($stats['performance_status'])) {
            echo "✅ Performance status: " . ucfirst($stats['performance_status']) . "\n";
        }
        
        // Check cache efficiency
        if (isset($stats['cache_efficiency'])) {
            echo "✅ Cache efficiency: " . ucfirst($stats['cache_efficiency']) . "\n";
        }
        
        // Check estimated time saved
        if (isset($stats['estimated_time_saved'])) {
            echo "✅ Estimated time saved: " . $stats['estimated_time_saved'] . "ms\n";
        }
        
        echo "\n";
        return true;
    }
    
    /**
     * Get cache key using reflection (for testing)
     */
    private function get_cache_key_via_reflection() {
        if (!$this->page_cache) {
            return null;
        }
        
        $reflection = new ReflectionClass($this->page_cache);
        $method = $reflection->getMethod('get_cache_key');
        $method->setAccessible(true);
        
        return $method->invoke($this->page_cache);
    }
    
    /**
     * Display test summary
     */
    private function display_test_summary($results) {
        echo "<h3>📋 Test Summary</h3>\n";
        
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $result) {
            if ($result) {
                echo "✅ " . ucfirst(str_replace('_', ' ', $test)) . " test passed\n";
                $passed++;
            } else {
                echo "❌ " . ucfirst(str_replace('_', ' ', $test)) . " test failed\n";
            }
        }
        
        echo "\n";
        echo "🎯 Overall Result: $passed/$total tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All cache improvements are working correctly!\n";
        } else {
            echo "⚠️ Some issues detected. Please review the failed tests above.\n";
        }
    }
}

// Auto-run tests if accessed directly (for debugging)
if (isset($_GET['run_cache_test']) && current_user_can('manage_options')) {
    $test = new Redco_Cache_Performance_Test();
    echo "<pre>";
    $test->run_tests();
    echo "</pre>";
}

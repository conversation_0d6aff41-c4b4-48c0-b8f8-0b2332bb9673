<?php
/**
 * Module settings tab template
 *
 * This template is used to render individual module settings pages.
 * Variables available: $module_key, $module_data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module settings
$module_settings = get_option('redco_optimizer_' . str_replace('-', '_', $module_key), array());
$is_enabled = redco_is_module_enabled($module_key);

// Initialize help system for use in module settings
$help_system = new Redco_Help_System();

/**
 * Helper function to render help icon
 */
function redco_help_icon($module, $section, $field = '', $tooltip = '') {
    $help_system = new Redco_Help_System();
    return $help_system->render_help_icon($module, $section, $field, $tooltip);
}
?>

<div class="redco-module-settings" data-module="<?php echo esc_attr($module_key); ?>">


    <?php if ($is_enabled): ?>
        <div class="module-settings-content">
            <?php
            // Load module-specific settings
            switch ($module_key) {
                case 'page-cache':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/page-cache/settings.php';
                    break;

                case 'lazy-load':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/lazy-load/settings.php';
                    break;

                case 'css-js-minifier':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/css-js-minifier/settings.php';
                    break;

                case 'database-cleanup':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/database-cleanup/settings.php';
                    break;

                case 'heartbeat-control':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/heartbeat-control/settings.php';
                    break;

                case 'wordpress-core-tweaks':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/wordpress-core-tweaks/settings.php';
                    break;

                case 'critical-resource-optimizer':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/critical-resource-optimizer/settings.php';
                    break;

                case 'diagnostic-autofix':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/tab.php';
                    break;

                case 'smart-webp-conversion':
                    include REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/settings.php';
                    break;

                default:
                    ?>
                    <div class="redco-notice info">
                        <p><?php _e('No settings available for this module.', 'redco-optimizer'); ?></p>
                    </div>
                    <?php
                    break;
            }
            ?>
        </div>
    <?php else: ?>
        <div class="module-disabled-notice">
            <div class="redco-notice warning">
                <p><?php _e('This module is currently disabled. Enable it to access settings.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.redco-module-settings {
    width: 100%;
    max-width: none;
}





.module-settings-content {
    background: #f8f9fa;
    padding: 0px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.module-disabled-notice {
    text-align: center;
    padding: 40px 20px;
}

.redco-form-section {
    margin-bottom: 30px;
}

.redco-form-section:last-child {
    margin-bottom: 0;
}

.redco-form-section h3 {
    margin: 0 0 15px 0;
    color: #32373c;
    font-size: 18px;
}

.redco-form-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.redco-form-row label {
    min-width: 200px;
    font-weight: 500;
    color: #32373c;
    padding-top: 5px;
}

.redco-form-row .form-control {
    flex: 1;
    max-width: 600px;
}

.redco-form-row .form-control input,
.redco-form-row .form-control select,
.redco-form-row .form-control textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.redco-form-row .form-control textarea {
    min-height: 80px;
    resize: vertical;
}

.redco-form-row .description {
    color: #666;
    font-size: 13px;
    font-style: italic;
    margin-top: 5px;
    line-height: 1.4;
}

.checkbox-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
}

.checkbox-item label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.button-secondary {
    background: #f8f9fa;
    border-color: #ddd;
    color: #495057;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    border: 1px solid #ddd;
    transition: all 0.2s ease;
    margin-right: 10px;
}

.button-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

@media (max-width: 768px) {
    .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .redco-form-row {
        flex-direction: column;
        gap: 5px;
    }

    .redco-form-row label {
        min-width: auto;
        padding-top: 0;
    }

    .redco-form-row .form-control {
        max-width: 100%;
    }

    .checkbox-list {
        grid-template-columns: 1fr;
    }
}
</style>

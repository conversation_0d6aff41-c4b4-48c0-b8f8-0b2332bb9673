/**
 * Frontend Performance Checker for Redco Optimizer
 *
 * This script helps identify performance issues on the frontend
 * Run in browser console on frontend pages to check for issues
 */

(function() {
    'use strict';

    // Create global debug object for frontend
    window.redcoFrontendDebug = {

        checkAssetLoading: function() {
            console.log('🔧 Checking frontend asset loading...');

            const issues = [];
            const assets = {
                scripts: [],
                styles: [],
                external: []
            };

            // Check all script tags
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                const src = script.src;

                if (src.includes('redco-optimizer')) {
                    assets.scripts.push({
                        src: src,
                        isAdmin: src.includes('admin'),
                        size: 'Unknown'
                    });

                    if (src.includes('admin')) {
                        issues.push(`❌ Admin script loading on frontend: ${src}`);
                    }
                }

                if (src.includes('chart') || src.includes('Chart.js')) {
                    assets.external.push({
                        src: src,
                        type: 'Chart.js'
                    });
                    issues.push(`⚠️ Chart.js loading on frontend: ${src}`);
                }
            });

            // Check all style tags
            const styles = document.querySelectorAll('link[rel="stylesheet"]');
            styles.forEach(style => {
                const href = style.href;

                if (href.includes('redco-optimizer')) {
                    assets.styles.push({
                        href: href,
                        isAdmin: href.includes('admin'),
                        size: 'Unknown'
                    });

                    if (href.includes('admin')) {
                        issues.push(`❌ Admin style loading on frontend: ${href}`);
                    }
                }
            });

            console.log('📊 Asset Analysis Results:');
            console.log('Scripts:', assets.scripts);
            console.log('Styles:', assets.styles);
            console.log('External:', assets.external);

            if (issues.length > 0) {
                console.log('\n❌ Issues Found:');
                issues.forEach(issue => console.log(issue));
            } else {
                console.log('\n✅ No asset loading issues detected');
            }

            return { assets, issues };
        },

        checkLazyLoading: function() {
            console.log('🔧 Checking lazy loading implementation...');

            const lazyImages = document.querySelectorAll('.redco-lazy');
            const allImages = document.querySelectorAll('img');
            const issues = [];

            console.log(`📊 Found ${allImages.length} total images, ${lazyImages.length} with lazy loading`);

            // Check if first images are excluded from lazy loading
            const firstImages = Array.from(allImages).slice(0, 3);
            let firstImagesLazy = 0;

            firstImages.forEach((img, index) => {
                if (img.classList.contains('redco-lazy')) {
                    firstImagesLazy++;
                    issues.push(`⚠️ First image (${index + 1}) has lazy loading - may affect LCP`);
                }
            });

            // Check for images without dimensions
            let imagesWithoutDimensions = 0;
            allImages.forEach(img => {
                if (!img.width || !img.height) {
                    imagesWithoutDimensions++;
                }
            });

            if (imagesWithoutDimensions > 0) {
                issues.push(`⚠️ ${imagesWithoutDimensions} images without dimensions - may cause CLS`);
            }

            console.log(`📈 Lazy loading coverage: ${((lazyImages.length / allImages.length) * 100).toFixed(1)}%`);
            console.log(`🎯 First 3 images with lazy loading: ${firstImagesLazy}/3`);

            if (issues.length > 0) {
                console.log('\n❌ Issues Found:');
                issues.forEach(issue => console.log(issue));
            } else {
                console.log('\n✅ Lazy loading implementation looks good');
            }

            return {
                totalImages: allImages.length,
                lazyImages: lazyImages.length,
                firstImagesLazy: firstImagesLazy,
                imagesWithoutDimensions: imagesWithoutDimensions,
                issues: issues
            };
        },

        measureCoreWebVitals: function() {
            console.log('🔧 Measuring Core Web Vitals...');

            const metrics = {};

            // Largest Contentful Paint
            if ('PerformanceObserver' in window) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        const lastEntry = entries[entries.length - 1];
                        metrics.lcp = lastEntry.startTime;
                        console.log(`🎯 LCP: ${metrics.lcp.toFixed(2)}ms`);

                        if (metrics.lcp > 2500) {
                            console.warn('⚠️ LCP is above 2.5s threshold');
                        } else {
                            console.log('✅ LCP is within good range');
                        }
                    });
                    observer.observe({ entryTypes: ['largest-contentful-paint'] });
                } catch (e) {
                    console.log('❌ LCP measurement not supported');
                }

                // First Input Delay
                try {
                    const fidObserver = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            metrics.fid = entry.processingStart - entry.startTime;
                            console.log(`⚡ FID: ${metrics.fid.toFixed(2)}ms`);

                            if (metrics.fid > 100) {
                                console.warn('⚠️ FID is above 100ms threshold');
                            } else {
                                console.log('✅ FID is within good range');
                            }
                        });
                    });
                    fidObserver.observe({ entryTypes: ['first-input'] });
                } catch (e) {
                    console.log('❌ FID measurement not supported');
                }

                // Cumulative Layout Shift
                try {
                    let clsValue = 0;
                    const clsObserver = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        });
                        metrics.cls = clsValue;
                        console.log(`📐 CLS: ${metrics.cls.toFixed(3)}`);

                        if (metrics.cls > 0.1) {
                            console.warn('⚠️ CLS is above 0.1 threshold');
                        } else {
                            console.log('✅ CLS is within good range');
                        }
                    });
                    clsObserver.observe({ entryTypes: ['layout-shift'] });
                } catch (e) {
                    console.log('❌ CLS measurement not supported');
                }
            } else {
                console.log('❌ PerformanceObserver not supported');
            }

            return metrics;
        },

        checkRenderBlocking: function() {
            console.log('🔧 Checking for render-blocking resources...');

            const renderBlocking = [];

            // Check for synchronous scripts in head
            const headScripts = document.head.querySelectorAll('script[src]');
            headScripts.forEach(script => {
                if (!script.async && !script.defer) {
                    renderBlocking.push({
                        type: 'script',
                        src: script.src,
                        issue: 'Synchronous script in head'
                    });
                }
            });

            // Check for CSS without media queries
            const styles = document.querySelectorAll('link[rel="stylesheet"]');
            styles.forEach(style => {
                if (!style.media || style.media === 'all') {
                    if (style.href.includes('redco-optimizer')) {
                        renderBlocking.push({
                            type: 'style',
                            href: style.href,
                            issue: 'CSS without media query optimization'
                        });
                    }
                }
            });

            if (renderBlocking.length > 0) {
                console.log('❌ Render-blocking resources found:');
                renderBlocking.forEach(resource => {
                    console.log(`  - ${resource.type}: ${resource.src || resource.href}`);
                    console.log(`    Issue: ${resource.issue}`);
                });
            } else {
                console.log('✅ No render-blocking resources detected');
            }

            return renderBlocking;
        },

        runFullAnalysis: function() {
            console.log('🚀 Running full frontend performance analysis...');
            console.log('='.repeat(60));

            const results = {
                assets: this.checkAssetLoading(),
                lazyLoading: this.checkLazyLoading(),
                renderBlocking: this.checkRenderBlocking()
            };

            // Start Core Web Vitals measurement
            this.measureCoreWebVitals();

            console.log('\n📊 SUMMARY:');

            const totalIssues = results.assets.issues.length +
                              results.lazyLoading.issues.length +
                              results.renderBlocking.length;

            if (totalIssues === 0) {
                console.log('🎉 No performance issues detected!');
            } else {
                console.log(`⚠️ ${totalIssues} performance issues found`);
                console.log('💡 Check the detailed analysis above for recommendations');
            }

            console.log('\n🔍 Core Web Vitals measurement started...');
            console.log('💡 Interact with the page to measure FID');
            console.log('⏱️ Wait a few seconds for LCP and CLS measurements');

            console.log('='.repeat(60));

            return results;
        },

        generateReport: function() {
            console.log('📋 Generating performance report...');

            const results = this.runFullAnalysis();

            // Create a simple report object
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                results: results,
                recommendations: []
            };

            // Generate recommendations
            if (results.assets.issues.length > 0) {
                report.recommendations.push('Fix admin assets loading on frontend');
            }

            if (results.lazyLoading.firstImagesLazy > 0) {
                report.recommendations.push('Exclude first images from lazy loading to improve LCP');
            }

            if (results.renderBlocking.length > 0) {
                report.recommendations.push('Optimize render-blocking resources');
            }

            console.log('📄 Performance Report Generated:');
            console.log(JSON.stringify(report, null, 2));

            return report;
        }
    };

    // Auto-run basic check if this is a Redco Optimizer site
    if (document.querySelector('script[src*="redco-optimizer"]') ||
        document.querySelector('link[href*="redco-optimizer"]')) {

        // Frontend debug tools available via redcoFrontendDebug object
    }

})();
